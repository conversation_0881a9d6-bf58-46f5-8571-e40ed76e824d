{"Version": 1, "WorkspaceRootPath": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3AEFB88A-7519-4717-ADD7-B24AEB5E697D}|MarriageBio.Shared\\MarriageBio.Shared.csproj|d:\\source code\\saas apps\\marriagebio\\marriagebio.shared\\marriagebio.shared\\configuration\\sharedconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3AEFB88A-7519-4717-ADD7-B24AEB5E697D}|MarriageBio.Shared\\MarriageBio.Shared.csproj|solutionrelative:marriagebio.shared\\configuration\\sharedconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2C90B9BB-1F51-5228-CC87-FDDCF219B4E9}|MarriageBio.Shared.Apis\\MarriageBio.Shared.Apis.csproj|d:\\source code\\saas apps\\marriagebio\\marriagebio.shared\\marriagebio.shared.apis\\webapplicationsharedbuilderextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2C90B9BB-1F51-5228-CC87-FDDCF219B4E9}|MarriageBio.Shared.Apis\\MarriageBio.Shared.Apis.csproj|solutionrelative:marriagebio.shared.apis\\webapplicationsharedbuilderextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\azure-pipelines.yml||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:azure-pipelines.yml||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{709AAD7A-D7EC-3FE5-C59D-CBC8F3C4C8C6}|MarriageBio.Shared.Functions\\MarriageBio.Shared.Functions.csproj|d:\\source code\\saas apps\\marriagebio\\marriagebio.shared\\marriagebio.shared.functions\\functionappsharedbuilderextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{709AAD7A-D7EC-3FE5-C59D-CBC8F3C4C8C6}|MarriageBio.Shared.Functions\\MarriageBio.Shared.Functions.csproj|solutionrelative:marriagebio.shared.functions\\functionappsharedbuilderextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2C90B9BB-1F51-5228-CC87-FDDCF219B4E9}|MarriageBio.Shared.Apis\\MarriageBio.Shared.Apis.csproj|d:\\source code\\saas apps\\marriagebio\\marriagebio.shared\\marriagebio.shared.apis\\globalexceptionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2C90B9BB-1F51-5228-CC87-FDDCF219B4E9}|MarriageBio.Shared.Apis\\MarriageBio.Shared.Apis.csproj|solutionrelative:marriagebio.shared.apis\\globalexceptionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 10, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:132:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "azure-pipelines.yml", "DocumentMoniker": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\azure-pipelines.yml", "RelativeDocumentMoniker": "azure-pipelines.yml", "ToolTip": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\azure-pipelines.yml", "RelativeToolTip": "azure-pipelines.yml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003775|", "WhenOpened": "2025-08-04T20:27:56.089Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "WebApplicationSharedBuilderExtensions.cs", "DocumentMoniker": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\MarriageBio.Shared.Apis\\WebApplicationSharedBuilderExtensions.cs", "RelativeDocumentMoniker": "MarriageBio.Shared.Apis\\WebApplicationSharedBuilderExtensions.cs", "ToolTip": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\MarriageBio.Shared.Apis\\WebApplicationSharedBuilderExtensions.cs", "RelativeToolTip": "MarriageBio.Shared.Apis\\WebApplicationSharedBuilderExtensions.cs", "ViewState": "AgIAABAAAAAAAAAAAAA5wBcAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T20:27:19.79Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "GlobalExceptionHandler.cs", "DocumentMoniker": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\MarriageBio.Shared.Apis\\GlobalExceptionHandler.cs", "RelativeDocumentMoniker": "MarriageBio.Shared.Apis\\GlobalExceptionHandler.cs", "ToolTip": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\MarriageBio.Shared.Apis\\GlobalExceptionHandler.cs", "RelativeToolTip": "MarriageBio.Shared.Apis\\GlobalExceptionHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T20:27:16.496Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "SharedConfig.cs", "DocumentMoniker": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\MarriageBio.Shared\\Configuration\\SharedConfig.cs", "RelativeDocumentMoniker": "MarriageBio.Shared\\Configuration\\SharedConfig.cs", "ToolTip": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\MarriageBio.Shared\\Configuration\\SharedConfig.cs", "RelativeToolTip": "MarriageBio.Shared\\Configuration\\SharedConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T20:27:14.347Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "FunctionAppSharedBuilderExtensions.cs", "DocumentMoniker": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\MarriageBio.Shared.Functions\\FunctionAppSharedBuilderExtensions.cs", "RelativeDocumentMoniker": "MarriageBio.Shared.Functions\\FunctionAppSharedBuilderExtensions.cs", "ToolTip": "D:\\Source Code\\Saas Apps\\MarriageBio\\MarriageBio.Shared\\MarriageBio.Shared.Functions\\FunctionAppSharedBuilderExtensions.cs", "RelativeToolTip": "MarriageBio.Shared.Functions\\FunctionAppSharedBuilderExtensions.cs", "ViewState": "AgIAADEAAAAAAAAAAAAkwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T20:27:06.658Z"}]}]}]}