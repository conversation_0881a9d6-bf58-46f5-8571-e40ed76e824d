import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, Subject } from 'rxjs';
import { AccountInfo, InteractionStatus } from '@azure/msal-browser';
import { MsalService, MsalBroadcastService } from '@azure/msal-angular';
import { filter, takeUntil } from 'rxjs/operators';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit, OnDestroy {
  title = 'MarriageBio.Web';
  
  isAuthenticated$: Observable<boolean>;
  userProfile$: Observable<AccountInfo | null>;
  private readonly _destroying$ = new Subject<void>();

  constructor(
    private authService: AuthService,
    private msalService: MsalService,
    private msalBroadcastService: MsalBroadcastService
  ) {
    this.isAuthenticated$ = this.authService.isAuthenticated$;
    this.userProfile$ = this.authService.userProfile$;
  }

  ngOnInit(): void {
    // Handle redirect response
    this.msalService.handleRedirectObservable().subscribe({
      next: (result) => {
        console.log('Redirect result:', result);
        if (result) {
          console.log('Login successful:', result);
        }
      },
      error: (error) => {
        console.error('Redirect error:', error);
      }
    });

    // Listen for interaction status changes
    this.msalBroadcastService.inProgress$
      .pipe(
        filter((status: InteractionStatus) => status === InteractionStatus.None),
        takeUntil(this._destroying$)
      )
      .subscribe(() => {
        console.log('Interaction completed');
      });

    // Debug authentication state
    this.isAuthenticated$.pipe(takeUntil(this._destroying$)).subscribe(isAuth => {
      console.log('Authentication status changed:', isAuth);
    });
  }

  ngOnDestroy(): void {
    this._destroying$.next(undefined);
    this._destroying$.complete();
  }

  login(): void {
    console.log('Login button clicked');
    try {
      this.authService.loginRedirect();
      console.log('Login redirect initiated');
    } catch (error) {
      console.error('Login error:', error);
      alert('Login failed: ' + error);
    }
  }

  logout(): void {
    console.log('Logout button clicked');
    try {
      this.authService.logout();
      console.log('Logout initiated');
    } catch (error) {
      console.error('Logout error:', error);
      alert('Logout failed: ' + error);
    }
  }
}
