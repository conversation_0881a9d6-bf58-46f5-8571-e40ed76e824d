{"version": 3, "sources": ["../../../../../../node_modules/@azure/msal-angular/fesm2020/azure-msal-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, Optional, Component, NgModule } from '@angular/core';\nimport { InteractionStatus, EventMessageUtils, WrapperSKU, InteractionType, BrowserConfigurationAuthError, UrlString, BrowserUtils, StringUtils, NavigationClient } from '@azure/msal-browser';\nimport { ReplaySubject, Subject, BehaviorSubject, from, of, EMPTY } from 'rxjs';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { map, concatMap, catchError, switchMap, take, filter } from 'rxjs/operators';\nimport * as i4 from '@angular/router';\n\n/* eslint-disable header/header */\nconst name = \"@azure/msal-angular\";\nconst version = \"4.0.17\";\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\nconst MSAL_INSTANCE = new InjectionToken(\"MSAL_INSTANCE\");\nconst MSAL_GUARD_CONFIG = new InjectionToken(\"MSAL_GUARD_CONFIG\");\nconst MSAL_INTERCEPTOR_CONFIG = new InjectionToken(\"MSAL_INTERCEPTOR_CONFIG\");\nconst MSAL_BROADCAST_CONFIG = new InjectionToken(\"MSAL_BROADCAST_CONFIG\");\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\nclass MsalBroadcastService {\n  constructor(msalInstance, msalBroadcastConfig) {\n    this.msalInstance = msalInstance;\n    this.msalBroadcastConfig = msalBroadcastConfig;\n    // Make _msalSubject a ReplaySubject if configured to replay past events\n    if (this.msalBroadcastConfig && this.msalBroadcastConfig.eventsToReplay > 0) {\n      this.msalInstance.getLogger().clone(name, version).verbose(`BroadcastService - eventsToReplay set on BroadcastConfig, replaying the last ${this.msalBroadcastConfig.eventsToReplay} events`);\n      this._msalSubject = new ReplaySubject(this.msalBroadcastConfig.eventsToReplay);\n    } else {\n      // Defaults to _msalSubject being a Subject\n      this._msalSubject = new Subject();\n    }\n    this.msalSubject$ = this._msalSubject.asObservable();\n    // InProgress as BehaviorSubject so most recent inProgress state will be available upon subscription\n    this._inProgress = new BehaviorSubject(InteractionStatus.Startup);\n    this.inProgress$ = this._inProgress.asObservable();\n    this.msalInstance.addEventCallback(message => {\n      this._msalSubject.next(message);\n      const status = EventMessageUtils.getInteractionStatusFromEvent(message, this._inProgress.value);\n      if (status !== null) {\n        this.msalInstance.getLogger().clone(name, version).verbose(`BroadcastService - ${message.eventType} results in setting inProgress from ${this._inProgress.value} to ${status}`);\n        this._inProgress.next(status);\n      }\n    });\n  }\n  /**\r\n   * Resets inProgress state to None\r\n   */\n  resetInProgressEvent() {\n    if (this._inProgress.value === InteractionStatus.Startup) {\n      this._inProgress.next(InteractionStatus.None);\n    }\n  }\n}\nMsalBroadcastService.ɵfac = function MsalBroadcastService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || MsalBroadcastService)(i0.ɵɵinject(MSAL_INSTANCE), i0.ɵɵinject(MSAL_BROADCAST_CONFIG, 8));\n};\nMsalBroadcastService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MsalBroadcastService,\n  factory: MsalBroadcastService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MsalBroadcastService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MSAL_INSTANCE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MSAL_BROADCAST_CONFIG]\n      }]\n    }];\n  }, null);\n})();\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\nclass MsalService {\n  constructor(instance, location, injector) {\n    this.instance = instance;\n    this.location = location;\n    this.injector = injector;\n    const hash = this.location.path(true).split(\"#\").pop();\n    if (hash) {\n      this.redirectHash = `#${hash}`;\n    }\n    this.instance.initializeWrapperLibrary(WrapperSKU.Angular, version);\n  }\n  initialize() {\n    return from(this.instance.initialize());\n  }\n  acquireTokenPopup(request) {\n    return from(this.instance.acquireTokenPopup(request));\n  }\n  acquireTokenRedirect(request) {\n    return from(this.instance.acquireTokenRedirect(request));\n  }\n  acquireTokenSilent(silentRequest) {\n    return from(this.instance.acquireTokenSilent(silentRequest));\n  }\n  handleRedirectObservable(hash) {\n    return from(this.instance.initialize().then(() => this.instance.handleRedirectPromise(hash || this.redirectHash)).finally(() => {\n      // update inProgress state to none\n      this.injector.get(MsalBroadcastService).resetInProgressEvent();\n    }));\n  }\n  loginPopup(request) {\n    return from(this.instance.loginPopup(request));\n  }\n  loginRedirect(request) {\n    return from(this.instance.loginRedirect(request));\n  }\n  // @deprecated: Use logoutRedirect or logoutPopup\n  logout(logoutRequest) {\n    return from(this.instance.logout(logoutRequest));\n  }\n  logoutRedirect(logoutRequest) {\n    return from(this.instance.logoutRedirect(logoutRequest));\n  }\n  logoutPopup(logoutRequest) {\n    return from(this.instance.logoutPopup(logoutRequest));\n  }\n  ssoSilent(request) {\n    return from(this.instance.ssoSilent(request));\n  }\n  /**\r\n   * Gets logger for msal-angular.\r\n   * If no logger set, returns logger instance created with same options as msal-browser\r\n   */\n  getLogger() {\n    if (!this.logger) {\n      this.logger = this.instance.getLogger().clone(name, version);\n    }\n    return this.logger;\n  }\n  // Create a logger instance for msal-angular with the same options as msal-browser\n  setLogger(logger) {\n    this.logger = logger.clone(name, version);\n    this.instance.setLogger(logger);\n  }\n}\nMsalService.ɵfac = function MsalService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || MsalService)(i0.ɵɵinject(MSAL_INSTANCE), i0.ɵɵinject(i3.Location), i0.ɵɵinject(i0.Injector));\n};\nMsalService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MsalService,\n  factory: MsalService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MsalService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MSAL_INSTANCE]\n      }]\n    }, {\n      type: i3.Location\n    }, {\n      type: i0.Injector\n    }];\n  }, null);\n})();\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\nclass MsalGuard {\n  constructor(msalGuardConfig, msalBroadcastService, authService, location, router) {\n    this.msalGuardConfig = msalGuardConfig;\n    this.msalBroadcastService = msalBroadcastService;\n    this.authService = authService;\n    this.location = location;\n    this.router = router;\n    // Subscribing so events in MsalGuard will set inProgress$ observable\n    this.msalBroadcastService.inProgress$.subscribe();\n  }\n  /**\r\n   * Parses url string to UrlTree\r\n   * @param url\r\n   */\n  parseUrl(url) {\n    return this.router.parseUrl(url);\n  }\n  /**\r\n   * Builds the absolute url for the destination page\r\n   * @param path Relative path of requested page\r\n   * @returns Full destination url\r\n   */\n  getDestinationUrl(path) {\n    this.authService.getLogger().verbose(\"Guard - getting destination url\");\n    // Absolute base url for the application (default to origin if base element not present)\n    const baseElements = document.getElementsByTagName(\"base\");\n    const baseUrl = this.location.normalize(baseElements.length ? baseElements[0].href : window.location.origin);\n    // Path of page (including hash, if using hash routing)\n    const pathUrl = this.location.prepareExternalUrl(path);\n    // Hash location strategy\n    if (pathUrl.startsWith(\"#\")) {\n      this.authService.getLogger().verbose(\"Guard - destination by hash routing\");\n      return `${baseUrl}/${pathUrl}`;\n    }\n    /*\r\n     * If using path location strategy, pathUrl will include the relative portion of the base path (e.g. /base/page).\r\n     * Since baseUrl also includes /base, can just concatentate baseUrl + path\r\n     */\n    return `${baseUrl}${path}`;\n  }\n  /**\r\n   * Interactively prompt the user to login\r\n   * @param url Path of the requested page\r\n   */\n  loginInteractively(state) {\n    const authRequest = typeof this.msalGuardConfig.authRequest === \"function\" ? this.msalGuardConfig.authRequest(this.authService, state) : {\n      ...this.msalGuardConfig.authRequest\n    };\n    if (this.msalGuardConfig.interactionType === InteractionType.Popup) {\n      this.authService.getLogger().verbose(\"Guard - logging in by popup\");\n      return this.authService.loginPopup(authRequest).pipe(map(response => {\n        this.authService.getLogger().verbose(\"Guard - login by popup successful, can activate, setting active account\");\n        this.authService.instance.setActiveAccount(response.account);\n        return true;\n      }));\n    }\n    this.authService.getLogger().verbose(\"Guard - logging in by redirect\");\n    const redirectStartPage = this.getDestinationUrl(state.url);\n    return this.authService.loginRedirect({\n      redirectStartPage,\n      ...authRequest\n    }).pipe(map(() => false));\n  }\n  /**\r\n   * Helper which checks for the correct interaction type, prevents page with Guard to be set as redirect, and calls handleRedirectObservable\r\n   * @param state\r\n   */\n  activateHelper(state) {\n    if (this.msalGuardConfig.interactionType !== InteractionType.Popup && this.msalGuardConfig.interactionType !== InteractionType.Redirect) {\n      throw new BrowserConfigurationAuthError(\"invalid_interaction_type\", \"Invalid interaction type provided to MSAL Guard. InteractionType.Popup or InteractionType.Redirect must be provided in the MsalGuardConfiguration\");\n    }\n    this.authService.getLogger().verbose(\"MSAL Guard activated\");\n    /*\r\n     * If a page with MSAL Guard is set as the redirect for acquireTokenSilent,\r\n     * short-circuit to prevent redirecting or popups.\r\n     */\n    if (typeof window !== \"undefined\") {\n      if (UrlString.hashContainsKnownProperties(window.location.hash) && BrowserUtils.isInIframe() && !this.authService.instance.getConfiguration().system.allowRedirectInIframe) {\n        this.authService.getLogger().warning(\"Guard - redirectUri set to page with MSAL Guard. It is recommended to not set redirectUri to a page that requires authentication.\");\n        return of(false);\n      }\n    } else {\n      this.authService.getLogger().info(\"Guard - window is undefined, MSAL does not support server-side token acquisition\");\n      return of(true);\n    }\n    /**\r\n     * If a loginFailedRoute is set in the config, set this as the loginFailedRoute\r\n     */\n    if (this.msalGuardConfig.loginFailedRoute) {\n      this.loginFailedRoute = this.parseUrl(this.msalGuardConfig.loginFailedRoute);\n    }\n    // Capture current path before it gets changed by handleRedirectObservable\n    const currentPath = this.location.path(true);\n    return this.authService.initialize().pipe(concatMap(() => {\n      return this.authService.handleRedirectObservable();\n    }), concatMap(() => {\n      if (!this.authService.instance.getAllAccounts().length) {\n        if (state) {\n          this.authService.getLogger().verbose(\"Guard - no accounts retrieved, log in required to activate\");\n          return this.loginInteractively(state);\n        }\n        this.authService.getLogger().verbose(\"Guard - no accounts retrieved, no state, cannot load\");\n        return of(false);\n      }\n      this.authService.getLogger().verbose(\"Guard - at least 1 account exists, can activate or load\");\n      // Prevent navigating the app to /#code= or /code=\n      if (state) {\n        /*\r\n         * Path routing:\r\n         * state.url: /#code=...\r\n         * state.root.fragment: code=...\r\n         */\n        /*\r\n         * Hash routing:\r\n         * state.url: /code\r\n         * state.root.fragment: null\r\n         */\n        const urlContainsCode = this.includesCode(state.url);\n        const fragmentContainsCode = !!state.root && !!state.root.fragment && this.includesCode(`#${state.root.fragment}`);\n        const hashRouting = this.location.prepareExternalUrl(state.url).indexOf(\"#\") === 0;\n        // Ensure code parameter is in fragment (and not in query parameter), or that hash hash routing is used\n        if (urlContainsCode && (fragmentContainsCode || hashRouting)) {\n          this.authService.getLogger().info(\"Guard - Hash contains known code response, stopping navigation.\");\n          // Path routing (navigate to current path without hash)\n          if (currentPath.indexOf(\"#\") > -1) {\n            return of(this.parseUrl(this.location.path()));\n          }\n          // Hash routing (navigate to root path)\n          return of(this.parseUrl(\"\"));\n        }\n      }\n      return of(true);\n    }), catchError(error => {\n      this.authService.getLogger().error(\"Guard - error while logging in, unable to activate\");\n      this.authService.getLogger().errorPii(`Guard - error: ${error.message}`);\n      /**\r\n       * If a loginFailedRoute is set, checks to see if state is passed before returning route\r\n       */\n      if (this.loginFailedRoute && state) {\n        this.authService.getLogger().verbose(\"Guard - loginFailedRoute set, redirecting\");\n        return of(this.loginFailedRoute);\n      }\n      return of(false);\n    }));\n  }\n  includesCode(path) {\n    return path.lastIndexOf(\"/code\") > -1 && path.lastIndexOf(\"/code\") === path.length - \"/code\".length ||\n    // path.endsWith(\"/code\")\n    path.indexOf(\"#code=\") > -1 || path.indexOf(\"&code=\") > -1;\n  }\n  canActivate(route, state) {\n    this.authService.getLogger().verbose(\"Guard - canActivate\");\n    return this.activateHelper(state);\n  }\n  canActivateChild(route, state) {\n    this.authService.getLogger().verbose(\"Guard - canActivateChild\");\n    return this.activateHelper(state);\n  }\n  canMatch() {\n    this.authService.getLogger().verbose(\"Guard - canLoad\");\n    return this.activateHelper();\n  }\n}\nMsalGuard.ɵfac = function MsalGuard_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || MsalGuard)(i0.ɵɵinject(MSAL_GUARD_CONFIG), i0.ɵɵinject(MsalBroadcastService), i0.ɵɵinject(MsalService), i0.ɵɵinject(i3.Location), i0.ɵɵinject(i4.Router));\n};\nMsalGuard.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MsalGuard,\n  factory: MsalGuard.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MsalGuard, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MSAL_GUARD_CONFIG]\n      }]\n    }, {\n      type: MsalBroadcastService\n    }, {\n      type: MsalService\n    }, {\n      type: i3.Location\n    }, {\n      type: i4.Router\n    }];\n  }, null);\n})();\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\nclass MsalInterceptor {\n  constructor(msalInterceptorConfig, authService, location, msalBroadcastService,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  document) {\n    this.msalInterceptorConfig = msalInterceptorConfig;\n    this.authService = authService;\n    this.location = location;\n    this.msalBroadcastService = msalBroadcastService;\n    this._document = document;\n  }\n  intercept(req,\n  // eslint-disable-line @typescript-eslint/no-explicit-any\n  next\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ) {\n    if (this.msalInterceptorConfig.interactionType !== InteractionType.Popup && this.msalInterceptorConfig.interactionType !== InteractionType.Redirect) {\n      throw new BrowserConfigurationAuthError(\"invalid_interaction_type\", \"Invalid interaction type provided to MSAL Interceptor. InteractionType.Popup, InteractionType.Redirect must be provided in the msalInterceptorConfiguration\");\n    }\n    this.authService.getLogger().verbose(\"MSAL Interceptor activated\");\n    const scopes = this.getScopesForEndpoint(req.url, req.method);\n    // If no scopes for endpoint, does not acquire token\n    if (!scopes || scopes.length === 0) {\n      this.authService.getLogger().verbose(\"Interceptor - no scopes for endpoint\");\n      return next.handle(req);\n    }\n    // Sets account as active account or first account\n    let account;\n    if (!!this.authService.instance.getActiveAccount()) {\n      this.authService.getLogger().verbose(\"Interceptor - active account selected\");\n      account = this.authService.instance.getActiveAccount();\n    } else {\n      this.authService.getLogger().verbose(\"Interceptor - no active account, fallback to first account\");\n      account = this.authService.instance.getAllAccounts()[0];\n    }\n    const authRequest = typeof this.msalInterceptorConfig.authRequest === \"function\" ? this.msalInterceptorConfig.authRequest(this.authService, req, {\n      account: account\n    }) : {\n      ...this.msalInterceptorConfig.authRequest,\n      account\n    };\n    this.authService.getLogger().info(`Interceptor - ${scopes.length} scopes found for endpoint`);\n    this.authService.getLogger().infoPii(`Interceptor - [${scopes}] scopes found for ${req.url}`);\n    return this.acquireToken(authRequest, scopes, account).pipe(switchMap(result => {\n      this.authService.getLogger().verbose(\"Interceptor - setting authorization headers\");\n      const headers = req.headers.set(\"Authorization\", `Bearer ${result.accessToken}`);\n      const requestClone = req.clone({\n        headers\n      });\n      return next.handle(requestClone);\n    }));\n  }\n  /**\r\n   * Try to acquire token silently. Invoke interaction if acquireTokenSilent rejected with error or resolved with null access token\r\n   * @param authRequest Request\r\n   * @param scopes Array of scopes for the request\r\n   * @param account Account\r\n   * @returns Authentication result\r\n   */\n  acquireToken(authRequest, scopes, account) {\n    // Note: For MSA accounts, include openid scope when calling acquireTokenSilent to return idToken\n    return this.authService.acquireTokenSilent({\n      ...authRequest,\n      scopes,\n      account\n    }).pipe(catchError(() => {\n      this.authService.getLogger().error(\"Interceptor - acquireTokenSilent rejected with error. Invoking interaction to resolve.\");\n      return this.msalBroadcastService.inProgress$.pipe(take(1), switchMap(status => {\n        if (status === InteractionStatus.None) {\n          return this.acquireTokenInteractively(authRequest, scopes);\n        }\n        return this.msalBroadcastService.inProgress$.pipe(filter(status => status === InteractionStatus.None), take(1), switchMap(() => this.acquireToken(authRequest, scopes, account)));\n      }));\n    }), switchMap(result => {\n      if (!result.accessToken) {\n        this.authService.getLogger().error(\"Interceptor - acquireTokenSilent resolved with null access token. Known issue with B2C tenants, invoking interaction to resolve.\");\n        return this.msalBroadcastService.inProgress$.pipe(filter(status => status === InteractionStatus.None), take(1), switchMap(() => this.acquireTokenInteractively(authRequest, scopes)));\n      }\n      return of(result);\n    }));\n  }\n  /**\r\n   * Invoke interaction for the given set of scopes\r\n   * @param authRequest Request\r\n   * @param scopes Array of scopes for the request\r\n   * @returns Result from the interactive request\r\n   */\n  acquireTokenInteractively(authRequest, scopes) {\n    if (this.msalInterceptorConfig.interactionType === InteractionType.Popup) {\n      this.authService.getLogger().verbose(\"Interceptor - error acquiring token silently, acquiring by popup\");\n      return this.authService.acquireTokenPopup({\n        ...authRequest,\n        scopes\n      });\n    }\n    this.authService.getLogger().verbose(\"Interceptor - error acquiring token silently, acquiring by redirect\");\n    const redirectStartPage = window.location.href;\n    this.authService.acquireTokenRedirect({\n      ...authRequest,\n      scopes,\n      redirectStartPage\n    });\n    return EMPTY;\n  }\n  /**\r\n   * Looks up the scopes for the given endpoint from the protectedResourceMap\r\n   * @param endpoint Url of the request\r\n   * @param httpMethod Http method of the request\r\n   * @returns Array of scopes, or null if not found\r\n   *\r\n   */\n  getScopesForEndpoint(endpoint, httpMethod) {\n    this.authService.getLogger().verbose(\"Interceptor - getting scopes for endpoint\");\n    // Ensures endpoints and protected resources compared are normalized\n    const normalizedEndpoint = this.location.normalize(endpoint);\n    const protectedResourcesArray = Array.from(this.msalInterceptorConfig.protectedResourceMap.keys());\n    const matchingProtectedResources = this.matchResourcesToEndpoint(protectedResourcesArray, normalizedEndpoint);\n    if (matchingProtectedResources.length > 0) {\n      return this.matchScopesToEndpoint(this.msalInterceptorConfig.protectedResourceMap, matchingProtectedResources, httpMethod);\n    }\n    return null;\n  }\n  /**\r\n   * Finds resource endpoints that match request endpoint\r\n   * @param protectedResourcesEndpoints\r\n   * @param endpoint\r\n   * @returns\r\n   */\n  matchResourcesToEndpoint(protectedResourcesEndpoints, endpoint) {\n    const matchingResources = [];\n    protectedResourcesEndpoints.forEach(key => {\n      const normalizedKey = this.location.normalize(key);\n      // Get url components\n      const absoluteKey = this.getAbsoluteUrl(normalizedKey);\n      const keyComponents = new URL(absoluteKey);\n      const absoluteEndpoint = this.getAbsoluteUrl(endpoint);\n      const endpointComponents = new URL(absoluteEndpoint);\n      if (this.checkUrlComponents(keyComponents, endpointComponents)) {\n        matchingResources.push(key);\n      }\n    });\n    return matchingResources;\n  }\n  /**\r\n   * Compares URL segments between key and endpoint\r\n   * @param key\r\n   * @param endpoint\r\n   * @returns\r\n   */\n  checkUrlComponents(keyComponents, endpointComponents) {\n    // URL properties from https://developer.mozilla.org/en-US/docs/Web/API/URL\n    const urlProperties = [\"protocol\", \"host\", \"pathname\", \"search\", \"hash\"];\n    for (const property of urlProperties) {\n      if (keyComponents[property]) {\n        const decodedInput = decodeURIComponent(keyComponents[property]);\n        if (!StringUtils.matchPattern(decodedInput, endpointComponents[property])) {\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n  /**\r\n   * Transforms relative urls to absolute urls\r\n   * @param url\r\n   * @returns\r\n   */\n  getAbsoluteUrl(url) {\n    const link = this._document.createElement(\"a\");\n    link.href = url;\n    return link.href;\n  }\n  /**\r\n   * Finds scopes from first matching endpoint with HTTP method that matches request\r\n   * @param protectedResourceMap Protected resource map\r\n   * @param endpointArray Array of resources that match request endpoint\r\n   * @param httpMethod Http method of the request\r\n   * @returns\r\n   */\n  matchScopesToEndpoint(protectedResourceMap, endpointArray, httpMethod) {\n    const allMatchedScopes = [];\n    // Check each matched endpoint for matching HttpMethod and scopes\n    endpointArray.forEach(matchedEndpoint => {\n      const scopesForEndpoint = [];\n      const methodAndScopesArray = protectedResourceMap.get(matchedEndpoint);\n      // Return if resource is unprotected\n      if (methodAndScopesArray === null) {\n        allMatchedScopes.push(null);\n        return;\n      }\n      methodAndScopesArray.forEach(entry => {\n        // Entry is either array of scopes or ProtectedResourceScopes object\n        if (typeof entry === \"string\") {\n          scopesForEndpoint.push(entry);\n        } else {\n          // Ensure methods being compared are normalized\n          const normalizedRequestMethod = httpMethod.toLowerCase();\n          const normalizedResourceMethod = entry.httpMethod.toLowerCase();\n          // Method in protectedResourceMap matches request http method\n          if (normalizedResourceMethod === normalizedRequestMethod) {\n            // Validate if scopes comes null to unprotect the resource in a certain http method\n            if (entry.scopes === null) {\n              allMatchedScopes.push(null);\n            } else {\n              entry.scopes.forEach(scope => {\n                scopesForEndpoint.push(scope);\n              });\n            }\n          }\n        }\n      });\n      // Only add to all scopes if scopes for endpoint and method is found\n      if (scopesForEndpoint.length > 0) {\n        allMatchedScopes.push(scopesForEndpoint);\n      }\n    });\n    if (allMatchedScopes.length > 0) {\n      if (allMatchedScopes.length > 1) {\n        this.authService.getLogger().warning(\"Interceptor - More than 1 matching scopes for endpoint found.\");\n      }\n      // Returns scopes for first matching endpoint\n      return allMatchedScopes[0];\n    }\n    return null;\n  }\n}\nMsalInterceptor.ɵfac = function MsalInterceptor_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || MsalInterceptor)(i0.ɵɵinject(MSAL_INTERCEPTOR_CONFIG), i0.ɵɵinject(MsalService), i0.ɵɵinject(i3.Location), i0.ɵɵinject(MsalBroadcastService), i0.ɵɵinject(DOCUMENT));\n};\nMsalInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MsalInterceptor,\n  factory: MsalInterceptor.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MsalInterceptor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MSAL_INTERCEPTOR_CONFIG]\n      }]\n    }, {\n      type: MsalService\n    }, {\n      type: i3.Location\n    }, {\n      type: MsalBroadcastService\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\n/**\r\n * This is a dedicated redirect component to be added to Angular apps to\r\n * handle redirects when using @azure/msal-angular.\r\n * Import this component to use redirects in your app.\r\n */\nclass MsalRedirectComponent {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  ngOnInit() {\n    this.authService.getLogger().verbose(\"MsalRedirectComponent activated\");\n    this.authService.handleRedirectObservable().subscribe();\n  }\n}\nMsalRedirectComponent.ɵfac = function MsalRedirectComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || MsalRedirectComponent)(i0.ɵɵdirectiveInject(MsalService));\n};\nMsalRedirectComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MsalRedirectComponent,\n  selectors: [[\"app-redirect\"]],\n  standalone: false,\n  decls: 0,\n  vars: 0,\n  template: function MsalRedirectComponent_Template(rf, ctx) {},\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MsalRedirectComponent, [{\n    type: Component,\n    args: [{\n      selector: \"app-redirect\",\n      template: \"\"\n    }]\n  }], function () {\n    return [{\n      type: MsalService\n    }];\n  }, null);\n})();\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\nclass MsalModule {\n  static forRoot(msalInstance, guardConfig, interceptorConfig) {\n    return {\n      ngModule: MsalModule,\n      providers: [{\n        provide: MSAL_INSTANCE,\n        useValue: msalInstance\n      }, {\n        provide: MSAL_GUARD_CONFIG,\n        useValue: guardConfig\n      }, {\n        provide: MSAL_INTERCEPTOR_CONFIG,\n        useValue: interceptorConfig\n      }, MsalService]\n    };\n  }\n}\nMsalModule.ɵfac = function MsalModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || MsalModule)();\n};\nMsalModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MsalModule,\n  declarations: [MsalRedirectComponent],\n  imports: [CommonModule]\n});\nMsalModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MsalGuard, MsalBroadcastService],\n  imports: [CommonModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MsalModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [MsalRedirectComponent],\n      imports: [CommonModule],\n      providers: [MsalGuard, MsalBroadcastService]\n    }]\n  }], null, null);\n})();\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\n/**\r\n * Custom navigation used for Angular client-side navigation.\r\n * See performance doc for details:\r\n * https://github.com/AzureAD/microsoft-authentication-library-for-js/tree/dev/lib/msal-angular/docs/performance.md\r\n */\nclass MsalCustomNavigationClient extends NavigationClient {\n  constructor(authService, router, location) {\n    super();\n    this.authService = authService;\n    this.router = router;\n    this.location = location;\n  }\n  async navigateInternal(url, options) {\n    this.authService.getLogger().trace(\"MsalCustomNavigationClient called\");\n    this.authService.getLogger().verbose(\"MsalCustomNavigationClient - navigating\");\n    this.authService.getLogger().verbosePii(`MsalCustomNavigationClient - navigating to url: ${url}`);\n    // Prevent hash clearing from causing an issue with Client-side navigation after redirect is handled\n    if (options.noHistory) {\n      return super.navigateInternal(url, options);\n    } else {\n      // Normalizing newUrl if no query string\n      const urlComponents = new UrlString(url).getUrlComponents();\n      const newUrl = urlComponents.QueryString ? `${urlComponents.AbsolutePath}?${urlComponents.QueryString}` : this.location.normalize(urlComponents.AbsolutePath);\n      await this.router.navigateByUrl(newUrl, {\n        replaceUrl: options.noHistory\n      });\n    }\n    return Promise.resolve(options.noHistory);\n  }\n}\nMsalCustomNavigationClient.ɵfac = function MsalCustomNavigationClient_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || MsalCustomNavigationClient)(i0.ɵɵinject(MsalService), i0.ɵɵinject(i4.Router), i0.ɵɵinject(i3.Location));\n};\nMsalCustomNavigationClient.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MsalCustomNavigationClient,\n  factory: MsalCustomNavigationClient.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MsalCustomNavigationClient, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: MsalService\n    }, {\n      type: i4.Router\n    }, {\n      type: i3.Location\n    }];\n  }, null);\n})();\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\n/**\r\n * @packageDocumentation\r\n * @module @azure/msal-angular\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { MSAL_BROADCAST_CONFIG, MSAL_GUARD_CONFIG, MSAL_INSTANCE, MSAL_INTERCEPTOR_CONFIG, MsalBroadcastService, MsalCustomNavigationClient, MsalGuard, MsalInterceptor, MsalModule, MsalRedirectComponent, MsalService, version };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,OAAO;AACb,IAAM,UAAU;AAMhB,IAAM,gBAAgB,IAAI,eAAe,eAAe;AACxD,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAChE,IAAM,0BAA0B,IAAI,eAAe,yBAAyB;AAC5E,IAAM,wBAAwB,IAAI,eAAe,uBAAuB;AAMxE,IAAM,uBAAN,MAA2B;AAAA,EACzB,YAAY,cAAc,qBAAqB;AAC7C,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAE3B,QAAI,KAAK,uBAAuB,KAAK,oBAAoB,iBAAiB,GAAG;AAC3E,WAAK,aAAa,UAAU,EAAE,MAAM,MAAM,OAAO,EAAE,QAAQ,gFAAgF,KAAK,oBAAoB,cAAc,SAAS;AAC3L,WAAK,eAAe,IAAI,cAAc,KAAK,oBAAoB,cAAc;AAAA,IAC/E,OAAO;AAEL,WAAK,eAAe,IAAI,QAAQ;AAAA,IAClC;AACA,SAAK,eAAe,KAAK,aAAa,aAAa;AAEnD,SAAK,cAAc,IAAI,gBAAgB,kBAAkB,OAAO;AAChE,SAAK,cAAc,KAAK,YAAY,aAAa;AACjD,SAAK,aAAa,iBAAiB,aAAW;AAC5C,WAAK,aAAa,KAAK,OAAO;AAC9B,YAAM,SAAS,kBAAkB,8BAA8B,SAAS,KAAK,YAAY,KAAK;AAC9F,UAAI,WAAW,MAAM;AACnB,aAAK,aAAa,UAAU,EAAE,MAAM,MAAM,OAAO,EAAE,QAAQ,sBAAsB,QAAQ,SAAS,uCAAuC,KAAK,YAAY,KAAK,OAAO,MAAM,EAAE;AAC9K,aAAK,YAAY,KAAK,MAAM;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACrB,QAAI,KAAK,YAAY,UAAU,kBAAkB,SAAS;AACxD,WAAK,YAAY,KAAK,kBAAkB,IAAI;AAAA,IAC9C;AAAA,EACF;AACF;AACA,qBAAqB,OAAO,SAAS,6BAA6B,mBAAmB;AACnF,SAAO,KAAK,qBAAqB,sBAAyB,SAAS,aAAa,GAAM,SAAS,uBAAuB,CAAC,CAAC;AAC1H;AACA,qBAAqB,QAA0B,mBAAmB;AAAA,EAChE,OAAO;AAAA,EACP,SAAS,qBAAqB;AAChC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,aAAa;AAAA,MACtB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,GAAG;AAAA,QACD,MAAM;AAAA,QACN,MAAM,CAAC,qBAAqB;AAAA,MAC9B,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAMH,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,UAAU,UAAU,UAAU;AACxC,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,UAAM,OAAO,KAAK,SAAS,KAAK,IAAI,EAAE,MAAM,GAAG,EAAE,IAAI;AACrD,QAAI,MAAM;AACR,WAAK,eAAe,IAAI,IAAI;AAAA,IAC9B;AACA,SAAK,SAAS,yBAAyB,WAAW,SAAS,OAAO;AAAA,EACpE;AAAA,EACA,aAAa;AACX,WAAO,KAAK,KAAK,SAAS,WAAW,CAAC;AAAA,EACxC;AAAA,EACA,kBAAkB,SAAS;AACzB,WAAO,KAAK,KAAK,SAAS,kBAAkB,OAAO,CAAC;AAAA,EACtD;AAAA,EACA,qBAAqB,SAAS;AAC5B,WAAO,KAAK,KAAK,SAAS,qBAAqB,OAAO,CAAC;AAAA,EACzD;AAAA,EACA,mBAAmB,eAAe;AAChC,WAAO,KAAK,KAAK,SAAS,mBAAmB,aAAa,CAAC;AAAA,EAC7D;AAAA,EACA,yBAAyB,MAAM;AAC7B,WAAO,KAAK,KAAK,SAAS,WAAW,EAAE,KAAK,MAAM,KAAK,SAAS,sBAAsB,QAAQ,KAAK,YAAY,CAAC,EAAE,QAAQ,MAAM;AAE9H,WAAK,SAAS,IAAI,oBAAoB,EAAE,qBAAqB;AAAA,IAC/D,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,WAAW,SAAS;AAClB,WAAO,KAAK,KAAK,SAAS,WAAW,OAAO,CAAC;AAAA,EAC/C;AAAA,EACA,cAAc,SAAS;AACrB,WAAO,KAAK,KAAK,SAAS,cAAc,OAAO,CAAC;AAAA,EAClD;AAAA;AAAA,EAEA,OAAO,eAAe;AACpB,WAAO,KAAK,KAAK,SAAS,OAAO,aAAa,CAAC;AAAA,EACjD;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,KAAK,KAAK,SAAS,eAAe,aAAa,CAAC;AAAA,EACzD;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,KAAK,KAAK,SAAS,YAAY,aAAa,CAAC;AAAA,EACtD;AAAA,EACA,UAAU,SAAS;AACjB,WAAO,KAAK,KAAK,SAAS,UAAU,OAAO,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS,KAAK,SAAS,UAAU,EAAE,MAAM,MAAM,OAAO;AAAA,IAC7D;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,UAAU,QAAQ;AAChB,SAAK,SAAS,OAAO,MAAM,MAAM,OAAO;AACxC,SAAK,SAAS,UAAU,MAAM;AAAA,EAChC;AACF;AACA,YAAY,OAAO,SAAS,oBAAoB,mBAAmB;AACjE,SAAO,KAAK,qBAAqB,aAAgB,SAAS,aAAa,GAAM,SAAY,QAAQ,GAAM,SAAY,QAAQ,CAAC;AAC9H;AACA,YAAY,QAA0B,mBAAmB;AAAA,EACvD,OAAO;AAAA,EACP,SAAS,YAAY;AACvB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,aAAa;AAAA,MACtB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAMH,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,iBAAiB,sBAAsB,aAAa,UAAU,QAAQ;AAChF,SAAK,kBAAkB;AACvB,SAAK,uBAAuB;AAC5B,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,SAAS;AAEd,SAAK,qBAAqB,YAAY,UAAU;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,KAAK;AACZ,WAAO,KAAK,OAAO,SAAS,GAAG;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,MAAM;AACtB,SAAK,YAAY,UAAU,EAAE,QAAQ,iCAAiC;AAEtE,UAAM,eAAe,SAAS,qBAAqB,MAAM;AACzD,UAAM,UAAU,KAAK,SAAS,UAAU,aAAa,SAAS,aAAa,CAAC,EAAE,OAAO,OAAO,SAAS,MAAM;AAE3G,UAAM,UAAU,KAAK,SAAS,mBAAmB,IAAI;AAErD,QAAI,QAAQ,WAAW,GAAG,GAAG;AAC3B,WAAK,YAAY,UAAU,EAAE,QAAQ,qCAAqC;AAC1E,aAAO,GAAG,OAAO,IAAI,OAAO;AAAA,IAC9B;AAKA,WAAO,GAAG,OAAO,GAAG,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,OAAO;AACxB,UAAM,cAAc,OAAO,KAAK,gBAAgB,gBAAgB,aAAa,KAAK,gBAAgB,YAAY,KAAK,aAAa,KAAK,IAAI,mBACpI,KAAK,gBAAgB;AAE1B,QAAI,KAAK,gBAAgB,oBAAoB,gBAAgB,OAAO;AAClE,WAAK,YAAY,UAAU,EAAE,QAAQ,6BAA6B;AAClE,aAAO,KAAK,YAAY,WAAW,WAAW,EAAE,KAAK,IAAI,cAAY;AACnE,aAAK,YAAY,UAAU,EAAE,QAAQ,yEAAyE;AAC9G,aAAK,YAAY,SAAS,iBAAiB,SAAS,OAAO;AAC3D,eAAO;AAAA,MACT,CAAC,CAAC;AAAA,IACJ;AACA,SAAK,YAAY,UAAU,EAAE,QAAQ,gCAAgC;AACrE,UAAM,oBAAoB,KAAK,kBAAkB,MAAM,GAAG;AAC1D,WAAO,KAAK,YAAY,cAAc;AAAA,MACpC;AAAA,OACG,YACJ,EAAE,KAAK,IAAI,MAAM,KAAK,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,OAAO;AACpB,QAAI,KAAK,gBAAgB,oBAAoB,gBAAgB,SAAS,KAAK,gBAAgB,oBAAoB,gBAAgB,UAAU;AACvI,YAAM,IAAI,8BAA8B,4BAA4B,mJAAmJ;AAAA,IACzN;AACA,SAAK,YAAY,UAAU,EAAE,QAAQ,sBAAsB;AAK3D,QAAI,OAAO,WAAW,aAAa;AACjC,UAAI,UAAU,4BAA4B,OAAO,SAAS,IAAI,KAAK,qBAAa,WAAW,KAAK,CAAC,KAAK,YAAY,SAAS,iBAAiB,EAAE,OAAO,uBAAuB;AAC1K,aAAK,YAAY,UAAU,EAAE,QAAQ,mIAAmI;AACxK,eAAO,GAAG,KAAK;AAAA,MACjB;AAAA,IACF,OAAO;AACL,WAAK,YAAY,UAAU,EAAE,KAAK,kFAAkF;AACpH,aAAO,GAAG,IAAI;AAAA,IAChB;AAIA,QAAI,KAAK,gBAAgB,kBAAkB;AACzC,WAAK,mBAAmB,KAAK,SAAS,KAAK,gBAAgB,gBAAgB;AAAA,IAC7E;AAEA,UAAM,cAAc,KAAK,SAAS,KAAK,IAAI;AAC3C,WAAO,KAAK,YAAY,WAAW,EAAE,KAAK,UAAU,MAAM;AACxD,aAAO,KAAK,YAAY,yBAAyB;AAAA,IACnD,CAAC,GAAG,UAAU,MAAM;AAClB,UAAI,CAAC,KAAK,YAAY,SAAS,eAAe,EAAE,QAAQ;AACtD,YAAI,OAAO;AACT,eAAK,YAAY,UAAU,EAAE,QAAQ,4DAA4D;AACjG,iBAAO,KAAK,mBAAmB,KAAK;AAAA,QACtC;AACA,aAAK,YAAY,UAAU,EAAE,QAAQ,sDAAsD;AAC3F,eAAO,GAAG,KAAK;AAAA,MACjB;AACA,WAAK,YAAY,UAAU,EAAE,QAAQ,yDAAyD;AAE9F,UAAI,OAAO;AAWT,cAAM,kBAAkB,KAAK,aAAa,MAAM,GAAG;AACnD,cAAM,uBAAuB,CAAC,CAAC,MAAM,QAAQ,CAAC,CAAC,MAAM,KAAK,YAAY,KAAK,aAAa,IAAI,MAAM,KAAK,QAAQ,EAAE;AACjH,cAAM,cAAc,KAAK,SAAS,mBAAmB,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM;AAEjF,YAAI,oBAAoB,wBAAwB,cAAc;AAC5D,eAAK,YAAY,UAAU,EAAE,KAAK,iEAAiE;AAEnG,cAAI,YAAY,QAAQ,GAAG,IAAI,IAAI;AACjC,mBAAO,GAAG,KAAK,SAAS,KAAK,SAAS,KAAK,CAAC,CAAC;AAAA,UAC/C;AAEA,iBAAO,GAAG,KAAK,SAAS,EAAE,CAAC;AAAA,QAC7B;AAAA,MACF;AACA,aAAO,GAAG,IAAI;AAAA,IAChB,CAAC,GAAG,WAAW,WAAS;AACtB,WAAK,YAAY,UAAU,EAAE,MAAM,oDAAoD;AACvF,WAAK,YAAY,UAAU,EAAE,SAAS,kBAAkB,MAAM,OAAO,EAAE;AAIvE,UAAI,KAAK,oBAAoB,OAAO;AAClC,aAAK,YAAY,UAAU,EAAE,QAAQ,2CAA2C;AAChF,eAAO,GAAG,KAAK,gBAAgB;AAAA,MACjC;AACA,aAAO,GAAG,KAAK;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,YAAY,OAAO,IAAI,MAAM,KAAK,YAAY,OAAO,MAAM,KAAK,SAAS,QAAQ;AAAA,IAE7F,KAAK,QAAQ,QAAQ,IAAI,MAAM,KAAK,QAAQ,QAAQ,IAAI;AAAA,EAC1D;AAAA,EACA,YAAY,OAAO,OAAO;AACxB,SAAK,YAAY,UAAU,EAAE,QAAQ,qBAAqB;AAC1D,WAAO,KAAK,eAAe,KAAK;AAAA,EAClC;AAAA,EACA,iBAAiB,OAAO,OAAO;AAC7B,SAAK,YAAY,UAAU,EAAE,QAAQ,0BAA0B;AAC/D,WAAO,KAAK,eAAe,KAAK;AAAA,EAClC;AAAA,EACA,WAAW;AACT,SAAK,YAAY,UAAU,EAAE,QAAQ,iBAAiB;AACtD,WAAO,KAAK,eAAe;AAAA,EAC7B;AACF;AACA,UAAU,OAAO,SAAS,kBAAkB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,WAAc,SAAS,iBAAiB,GAAM,SAAS,oBAAoB,GAAM,SAAS,WAAW,GAAM,SAAY,QAAQ,GAAM,SAAY,MAAM,CAAC;AAC3L;AACA,UAAU,QAA0B,mBAAmB;AAAA,EACrD,OAAO;AAAA,EACP,SAAS,UAAU;AACrB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,iBAAiB;AAAA,MAC1B,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAMH,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,uBAAuB,aAAa,UAAU,sBAE1DA,WAAU;AACR,SAAK,wBAAwB;AAC7B,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,uBAAuB;AAC5B,SAAK,YAAYA;AAAA,EACnB;AAAA,EACA,UAAU,KAEV,MAEE;AACA,QAAI,KAAK,sBAAsB,oBAAoB,gBAAgB,SAAS,KAAK,sBAAsB,oBAAoB,gBAAgB,UAAU;AACnJ,YAAM,IAAI,8BAA8B,4BAA4B,6JAA6J;AAAA,IACnO;AACA,SAAK,YAAY,UAAU,EAAE,QAAQ,4BAA4B;AACjE,UAAM,SAAS,KAAK,qBAAqB,IAAI,KAAK,IAAI,MAAM;AAE5D,QAAI,CAAC,UAAU,OAAO,WAAW,GAAG;AAClC,WAAK,YAAY,UAAU,EAAE,QAAQ,sCAAsC;AAC3E,aAAO,KAAK,OAAO,GAAG;AAAA,IACxB;AAEA,QAAI;AACJ,QAAI,CAAC,CAAC,KAAK,YAAY,SAAS,iBAAiB,GAAG;AAClD,WAAK,YAAY,UAAU,EAAE,QAAQ,uCAAuC;AAC5E,gBAAU,KAAK,YAAY,SAAS,iBAAiB;AAAA,IACvD,OAAO;AACL,WAAK,YAAY,UAAU,EAAE,QAAQ,4DAA4D;AACjG,gBAAU,KAAK,YAAY,SAAS,eAAe,EAAE,CAAC;AAAA,IACxD;AACA,UAAM,cAAc,OAAO,KAAK,sBAAsB,gBAAgB,aAAa,KAAK,sBAAsB,YAAY,KAAK,aAAa,KAAK;AAAA,MAC/I;AAAA,IACF,CAAC,IAAI,iCACA,KAAK,sBAAsB,cAD3B;AAAA,MAEH;AAAA,IACF;AACA,SAAK,YAAY,UAAU,EAAE,KAAK,iBAAiB,OAAO,MAAM,4BAA4B;AAC5F,SAAK,YAAY,UAAU,EAAE,QAAQ,kBAAkB,MAAM,sBAAsB,IAAI,GAAG,EAAE;AAC5F,WAAO,KAAK,aAAa,aAAa,QAAQ,OAAO,EAAE,KAAK,UAAU,YAAU;AAC9E,WAAK,YAAY,UAAU,EAAE,QAAQ,6CAA6C;AAClF,YAAM,UAAU,IAAI,QAAQ,IAAI,iBAAiB,UAAU,OAAO,WAAW,EAAE;AAC/E,YAAM,eAAe,IAAI,MAAM;AAAA,QAC7B;AAAA,MACF,CAAC;AACD,aAAO,KAAK,OAAO,YAAY;AAAA,IACjC,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,aAAa,QAAQ,SAAS;AAEzC,WAAO,KAAK,YAAY,mBAAmB,iCACtC,cADsC;AAAA,MAEzC;AAAA,MACA;AAAA,IACF,EAAC,EAAE,KAAK,WAAW,MAAM;AACvB,WAAK,YAAY,UAAU,EAAE,MAAM,wFAAwF;AAC3H,aAAO,KAAK,qBAAqB,YAAY,KAAK,KAAK,CAAC,GAAG,UAAU,YAAU;AAC7E,YAAI,WAAW,kBAAkB,MAAM;AACrC,iBAAO,KAAK,0BAA0B,aAAa,MAAM;AAAA,QAC3D;AACA,eAAO,KAAK,qBAAqB,YAAY,KAAK,OAAO,CAAAC,YAAUA,YAAW,kBAAkB,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,MAAM,KAAK,aAAa,aAAa,QAAQ,OAAO,CAAC,CAAC;AAAA,MAClL,CAAC,CAAC;AAAA,IACJ,CAAC,GAAG,UAAU,YAAU;AACtB,UAAI,CAAC,OAAO,aAAa;AACvB,aAAK,YAAY,UAAU,EAAE,MAAM,kIAAkI;AACrK,eAAO,KAAK,qBAAqB,YAAY,KAAK,OAAO,YAAU,WAAW,kBAAkB,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,MAAM,KAAK,0BAA0B,aAAa,MAAM,CAAC,CAAC;AAAA,MACtL;AACA,aAAO,GAAG,MAAM;AAAA,IAClB,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0BAA0B,aAAa,QAAQ;AAC7C,QAAI,KAAK,sBAAsB,oBAAoB,gBAAgB,OAAO;AACxE,WAAK,YAAY,UAAU,EAAE,QAAQ,kEAAkE;AACvG,aAAO,KAAK,YAAY,kBAAkB,iCACrC,cADqC;AAAA,QAExC;AAAA,MACF,EAAC;AAAA,IACH;AACA,SAAK,YAAY,UAAU,EAAE,QAAQ,qEAAqE;AAC1G,UAAM,oBAAoB,OAAO,SAAS;AAC1C,SAAK,YAAY,qBAAqB,iCACjC,cADiC;AAAA,MAEpC;AAAA,MACA;AAAA,IACF,EAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,UAAU,YAAY;AACzC,SAAK,YAAY,UAAU,EAAE,QAAQ,2CAA2C;AAEhF,UAAM,qBAAqB,KAAK,SAAS,UAAU,QAAQ;AAC3D,UAAM,0BAA0B,MAAM,KAAK,KAAK,sBAAsB,qBAAqB,KAAK,CAAC;AACjG,UAAM,6BAA6B,KAAK,yBAAyB,yBAAyB,kBAAkB;AAC5G,QAAI,2BAA2B,SAAS,GAAG;AACzC,aAAO,KAAK,sBAAsB,KAAK,sBAAsB,sBAAsB,4BAA4B,UAAU;AAAA,IAC3H;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB,6BAA6B,UAAU;AAC9D,UAAM,oBAAoB,CAAC;AAC3B,gCAA4B,QAAQ,SAAO;AACzC,YAAM,gBAAgB,KAAK,SAAS,UAAU,GAAG;AAEjD,YAAM,cAAc,KAAK,eAAe,aAAa;AACrD,YAAM,gBAAgB,IAAI,IAAI,WAAW;AACzC,YAAM,mBAAmB,KAAK,eAAe,QAAQ;AACrD,YAAM,qBAAqB,IAAI,IAAI,gBAAgB;AACnD,UAAI,KAAK,mBAAmB,eAAe,kBAAkB,GAAG;AAC9D,0BAAkB,KAAK,GAAG;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,eAAe,oBAAoB;AAEpD,UAAM,gBAAgB,CAAC,YAAY,QAAQ,YAAY,UAAU,MAAM;AACvE,eAAW,YAAY,eAAe;AACpC,UAAI,cAAc,QAAQ,GAAG;AAC3B,cAAM,eAAe,mBAAmB,cAAc,QAAQ,CAAC;AAC/D,YAAI,CAAC,YAAY,aAAa,cAAc,mBAAmB,QAAQ,CAAC,GAAG;AACzE,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,KAAK;AAClB,UAAM,OAAO,KAAK,UAAU,cAAc,GAAG;AAC7C,SAAK,OAAO;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,sBAAsB,eAAe,YAAY;AACrE,UAAM,mBAAmB,CAAC;AAE1B,kBAAc,QAAQ,qBAAmB;AACvC,YAAM,oBAAoB,CAAC;AAC3B,YAAM,uBAAuB,qBAAqB,IAAI,eAAe;AAErE,UAAI,yBAAyB,MAAM;AACjC,yBAAiB,KAAK,IAAI;AAC1B;AAAA,MACF;AACA,2BAAqB,QAAQ,WAAS;AAEpC,YAAI,OAAO,UAAU,UAAU;AAC7B,4BAAkB,KAAK,KAAK;AAAA,QAC9B,OAAO;AAEL,gBAAM,0BAA0B,WAAW,YAAY;AACvD,gBAAM,2BAA2B,MAAM,WAAW,YAAY;AAE9D,cAAI,6BAA6B,yBAAyB;AAExD,gBAAI,MAAM,WAAW,MAAM;AACzB,+BAAiB,KAAK,IAAI;AAAA,YAC5B,OAAO;AACL,oBAAM,OAAO,QAAQ,WAAS;AAC5B,kCAAkB,KAAK,KAAK;AAAA,cAC9B,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,kBAAkB,SAAS,GAAG;AAChC,yBAAiB,KAAK,iBAAiB;AAAA,MACzC;AAAA,IACF,CAAC;AACD,QAAI,iBAAiB,SAAS,GAAG;AAC/B,UAAI,iBAAiB,SAAS,GAAG;AAC/B,aAAK,YAAY,UAAU,EAAE,QAAQ,+DAA+D;AAAA,MACtG;AAEA,aAAO,iBAAiB,CAAC;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACF;AACA,gBAAgB,OAAO,SAAS,wBAAwB,mBAAmB;AACzE,SAAO,KAAK,qBAAqB,iBAAoB,SAAS,uBAAuB,GAAM,SAAS,WAAW,GAAM,SAAY,QAAQ,GAAM,SAAS,oBAAoB,GAAM,SAAS,QAAQ,CAAC;AACtM;AACA,gBAAgB,QAA0B,mBAAmB;AAAA,EAC3D,OAAO;AAAA,EACP,SAAS,gBAAgB;AAC3B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,uBAAuB;AAAA,MAChC,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAWH,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,WAAW;AACT,SAAK,YAAY,UAAU,EAAE,QAAQ,iCAAiC;AACtE,SAAK,YAAY,yBAAyB,EAAE,UAAU;AAAA,EACxD;AACF;AACA,sBAAsB,OAAO,SAAS,8BAA8B,mBAAmB;AACrF,SAAO,KAAK,qBAAqB,uBAA0B,kBAAkB,WAAW,CAAC;AAC3F;AACA,sBAAsB,OAAyB,kBAAkB;AAAA,EAC/D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,EAC5B,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AAAA,EAAC;AAAA,EAC5D,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAMH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,QAAQ,cAAc,aAAa,mBAAmB;AAC3D,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,WAAW;AAAA,IAChB;AAAA,EACF;AACF;AACA,WAAW,OAAO,SAAS,mBAAmB,mBAAmB;AAC/D,SAAO,KAAK,qBAAqB,YAAY;AAC/C;AACA,WAAW,OAAyB,iBAAiB;AAAA,EACnD,MAAM;AAAA,EACN,cAAc,CAAC,qBAAqB;AAAA,EACpC,SAAS,CAAC,YAAY;AACxB,CAAC;AACD,WAAW,OAAyB,iBAAiB;AAAA,EACnD,WAAW,CAAC,WAAW,oBAAoB;AAAA,EAC3C,SAAS,CAAC,YAAY;AACxB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,qBAAqB;AAAA,MACpC,SAAS,CAAC,YAAY;AAAA,MACtB,WAAW,CAAC,WAAW,oBAAoB;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAWH,IAAM,6BAAN,MAAM,oCAAmC,iBAAiB;AAAA,EACxD,YAAY,aAAa,QAAQ,UAAU;AACzC,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EAClB;AAAA,EACM,iBAAiB,KAAK,SAAS;AAAA;AACnC,WAAK,YAAY,UAAU,EAAE,MAAM,mCAAmC;AACtE,WAAK,YAAY,UAAU,EAAE,QAAQ,yCAAyC;AAC9E,WAAK,YAAY,UAAU,EAAE,WAAW,mDAAmD,GAAG,EAAE;AAEhG,UAAI,QAAQ,WAAW;AACrB,eAAO,wDAAM,yBAAN,MAAuB,KAAK,OAAO;AAAA,MAC5C,OAAO;AAEL,cAAM,gBAAgB,IAAI,UAAU,GAAG,EAAE,iBAAiB;AAC1D,cAAM,SAAS,cAAc,cAAc,GAAG,cAAc,YAAY,IAAI,cAAc,WAAW,KAAK,KAAK,SAAS,UAAU,cAAc,YAAY;AAC5J,cAAM,KAAK,OAAO,cAAc,QAAQ;AAAA,UACtC,YAAY,QAAQ;AAAA,QACtB,CAAC;AAAA,MACH;AACA,aAAO,QAAQ,QAAQ,QAAQ,SAAS;AAAA,IAC1C;AAAA;AACF;AACA,2BAA2B,OAAO,SAAS,mCAAmC,mBAAmB;AAC/F,SAAO,KAAK,qBAAqB,4BAA+B,SAAS,WAAW,GAAM,SAAY,MAAM,GAAM,SAAY,QAAQ,CAAC;AACzI;AACA,2BAA2B,QAA0B,mBAAmB;AAAA,EACtE,OAAO;AAAA,EACP,SAAS,2BAA2B;AACtC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;", "names": ["document", "status"]}