import { BrowserConfiguration, Configuration } from "../../config/Configuration.js";
export type CustomAuthOptions = {
    challengeTypes?: Array<string>;
    authApiProxyUrl: string;
};
export type CustomAuthConfiguration = Configuration & {
    customAuth: CustomAuthOptions;
};
export type CustomAuthBrowserConfiguration = BrowserConfiguration & {
    customAuth: CustomAuthOptions;
};
//# sourceMappingURL=CustomAuthConfiguration.d.ts.map