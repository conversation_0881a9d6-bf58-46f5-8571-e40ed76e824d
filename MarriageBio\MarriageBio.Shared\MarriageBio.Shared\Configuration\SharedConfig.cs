namespace MarriageBio.Shared.Configuration
{
    /// <summary>
    /// Optimized SharedConfig with base values only.
    /// Derived values (<PERSON><PERSON>, Authority, SignedOutCallbackPath) are computed from base values.
    /// </summary>
    public class SharedConfig
    {
        public string ApiBaseUrl { get; set; }

        public AzureAdB2CConfig AzureAdB2C { get; set; }
        public AwsConfig Aws { get; set; }
        public AzureConfig Azure { get; set; }
        public string Sentinel { get; set; }
    }

    public class AzureAdB2CConfig
    {
        /// <summary>API Client ID</summary>
        public string ApiClientId { get; set; }

        /// <summary>Azure B2C Domain</summary>
        public string Domain { get; set; }

        /// <summary>Azure B2C Instance URL</summary>
        public string Instance { get; set; }

        /// <summary>Member Type Role Claim Free Value</summary>
        public string MemberTypeRoleClaimFreeValue { get; set; }

        /// <summary>Member Type Role Claim Name</summary>
        public string MemberTypeRoleClaimName { get; set; }

        /// <summary>Member Type Role Claim Paid Value</summary>
        public string MemberTypeRoleClaimPaidValue { get; set; }

        /// <summary>Sign Up Sign In Policy ID</summary>
        public string SignUpSignInPolicyId { get; set; }

        /// <summary>Web Client ID</summary>
        public string WebClientId { get; set; }

        // Computed properties - derived from base values

        /// <summary>Computed: API scope derived from Domain and ResourceNamePrefix</summary>
        public string GetApiScope(string resourceNamePrefix) => $"https://{Domain}/{resourceNamePrefix}-api/access";

        /// <summary>Computed: Signed out callback path derived from Policy</summary>
        public string SignedOutCallbackPath => $"/signout/{SignUpSignInPolicyId}";

        /// <summary>Computed: Web Authority derived from Instance, Domain, and Policy</summary>
        public string WebAuthority => $"{Instance}/{Domain}/{SignUpSignInPolicyId}";
    }



    /// <summary>
    /// AWS configuration settings
    /// </summary>
    public class AwsConfig
    {
        /// <summary>AWS Access Key ID</summary>
        public string AccessKeyId { get; set; }

        /// <summary>AWS Account ID</summary>
        public string AccountId { get; set; }

        /// <summary>AWS Region</summary>
        public string Region { get; set; }

        /// <summary>AWS Secret Access Key (typically stored in Key Vault)</summary>
        public string SecretAccessKey { get; set; }
    }

    /// <summary>
    /// Azure configuration settings
    /// </summary>
    public class AzureConfig
    {
        /// <summary>Application Insights Connection String (typically stored in Key Vault)</summary>
        public string ApplicationInsightsConnectionString { get; set; }

        /// <summary>Azure Resource Name Prefix</summary>
        public string ResourceNamePrefix { get; set; }
    }
}

