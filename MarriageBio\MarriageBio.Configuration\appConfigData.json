{"$schema": "http://azconfig.io/schemas/KVSet/v1.0.0/KVSet.json", "items": [{"key": "MarriageBio:Shared:Sentinel", "value": "", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:Azure:ResourceNamePrefix", "value": "marriage-bio", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:Azure:ApplicationInsightsConnectionString", "value": "{\"uri\":\"https://common-kv.vault.azure.net/secrets/MarriageBioApplicationInsightsConnectionString\"}", "label": "", "content_type": "application/vnd.microsoft.appconfig.keyvaultref+json;charset=utf-8", "tags": {}}, {"key": "MarriageBio:Shared:AzureAdB2C:Domain", "value": "commonb2c5047.onmicrosoft.com", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:AzureAdB2C:Instance", "value": "https://commonb2c5047.b2clogin.com", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:AzureAdB2C:SignUpSignInPolicyId", "value": "B2C_1_signupsignin", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:AzureAdB2C:ApiClientId", "value": "122316e1-be79-42ea-813d-8791c8479353", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:AzureAdB2C:WebClientId", "value": "dc7ce323-2e2e-4155-8de7-116b12182747", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:AzureAdB2C:MemberTypeRoleClaimName", "value": "extension_MemberType", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:AzureAdB2C:MemberTypeRoleClaimFreeValue", "value": "Free", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:AzureAdB2C:MemberTypeRoleClaimPaidValue", "value": "Paid", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:Aws:Region", "value": "ap-south-1", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:Aws:AccountId", "value": "************", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:Aws:AccessKeyId", "value": "AKIA5PHLJWCVMF3DQQP6", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:Shared:Aws:SecretAccessKey", "value": "{\"uri\":\"https://common-kv.vault.azure.net/secrets/AwsSecretAccessKey\"}", "label": "", "content_type": "application/vnd.microsoft.appconfig.keyvaultref+json;charset=utf-8", "tags": {}}, {"key": "MarriageBio:PdfService:ApiBaseUrl", "value": "", "label": "", "content_type": "text/plain", "tags": {}}, {"key": "MarriageBio:PdfService:RecordsToReturnCount", "value": "10", "label": "", "content_type": "text/plain", "tags": {}}]}