import { Injectable } from '@angular/core';
import { MsalService, MsalBroadcastService } from '@azure/msal-angular';
import { 
  AuthenticationResult, 
  PopupRequest, 
  RedirectRequest, 
  SilentRequest,
  AccountInfo,
  InteractionStatus,
  EventMessage,
  EventType
} from '@azure/msal-browser';
import { Observable, filter, takeUntil, Subject, BehaviorSubject } from 'rxjs';
import { loginRequest, b2cPolicies } from '../config/auth-config';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly _destroying$ = new Subject<void>();
  private _isAuthenticated$ = new BehaviorSubject<boolean>(false);
  private _userProfile$ = new BehaviorSubject<AccountInfo | null>(null);

  constructor(
    private msalService: MsalService,
    private msalBroadcastService: MsalBroadcastService
  ) {
    this.initializeAuth();
  }

  /**
   * Initialize authentication state
   */
  private initializeAuth(): void {
    // Listen for login/logout events
    this.msalBroadcastService.msalSubject$
      .pipe(
        filter((msg: EventMessage) => 
          msg.eventType === EventType.LOGIN_SUCCESS || 
          msg.eventType === EventType.LOGOUT_SUCCESS ||
          msg.eventType === EventType.ACQUIRE_TOKEN_SUCCESS
        ),
        takeUntil(this._destroying$)
      )
      .subscribe((_result: EventMessage) => {
        this.updateAuthenticationState();
      });

    // Listen for interaction status changes
    this.msalBroadcastService.inProgress$
      .pipe(
        filter((status: InteractionStatus) => status === InteractionStatus.None),
        takeUntil(this._destroying$)
      )
      .subscribe(() => {
        this.updateAuthenticationState();
      });

    // Initial authentication state check
    this.updateAuthenticationState();
  }

  /**
   * Update authentication state based on current accounts
   */
  private updateAuthenticationState(): void {
    const accounts = this.msalService.instance.getAllAccounts();
    const isAuthenticated = accounts.length > 0;
    
    this._isAuthenticated$.next(isAuthenticated);
    this._userProfile$.next(isAuthenticated ? accounts[0] : null);
  }

  /**
   * Observable for authentication status
   */
  get isAuthenticated$(): Observable<boolean> {
    return this._isAuthenticated$.asObservable();
  }

  /**
   * Observable for user profile
   */
  get userProfile$(): Observable<AccountInfo | null> {
    return this._userProfile$.asObservable();
  }

  /**
   * Get current authentication status
   */
  get isAuthenticated(): boolean {
    return this._isAuthenticated$.value;
  }

  /**
   * Get current user profile
   */
  get userProfile(): AccountInfo | null {
    return this._userProfile$.value;
  }

  /**
   * Login using redirect
   */
  loginRedirect(): void {
    console.log('AuthService: loginRedirect called');
    try {
      const redirectRequest: RedirectRequest = {
        ...loginRequest,
        authority: b2cPolicies.authorities.signUpSignIn.authority
      };

      console.log('AuthService: Redirect request:', redirectRequest);
      this.msalService.loginRedirect(redirectRequest);
      console.log('AuthService: loginRedirect completed');
    } catch (error) {
      console.error('AuthService: loginRedirect error:', error);
      throw error;
    }
  }

  /**
   * Login using popup
   */
  loginPopup(): Observable<AuthenticationResult> {
    const popupRequest: PopupRequest = {
      ...loginRequest,
      authority: b2cPolicies.authorities.signUpSignIn.authority
    };

    return this.msalService.loginPopup(popupRequest);
  }

  /**
   * Logout
   */
  logout(): void {
    this.msalService.logoutRedirect({
      postLogoutRedirectUri: '/'
    });
  }

  /**
   * Get access token silently
   */
  getAccessToken(): Observable<AuthenticationResult> {
    const account = this.msalService.instance.getActiveAccount();
    
    if (!account) {
      throw new Error('No active account found');
    }

    const silentRequest: SilentRequest = {
      scopes: loginRequest.scopes,
      account: account,
      authority: b2cPolicies.authorities.signUpSignIn.authority
    };

    return this.msalService.acquireTokenSilent(silentRequest);
  }

  /**
   * Clean up subscriptions
   */
  ngOnDestroy(): void {
    this._destroying$.next(undefined);
    this._destroying$.complete();
  }
}
