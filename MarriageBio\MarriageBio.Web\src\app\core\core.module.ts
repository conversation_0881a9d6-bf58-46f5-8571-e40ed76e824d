import { NgModule, Optional, SkipSelf } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS } from '@angular/common/http';

// MSAL imports
import { 
  MSAL_INSTANCE, 
  MSAL_GUARD_CONFIG, 
  MSAL_INTERCEPTOR_CONFIG, 
  MsalService, 
  MsalGuard, 
  MsalBroadcastService,
  MsalGuardConfiguration,
  MsalInterceptorConfiguration
} from '@azure/msal-angular';
import { 
  IPublicClientApplication, 
  PublicClientApplication, 
  InteractionType
} from '@azure/msal-browser';

// Local imports
import { AuthService } from './services/auth.service';
import { AuthGuard } from './guards/auth.guard';
import { msalConfig, loginRequest } from './config/auth-config';

/**
 * Core module that should be imported only once in the app module.
 * Contains singleton services, guards, and interceptors.
 */
@NgModule({
  imports: [CommonModule],
  providers: [
    // MSAL Configuration
    {
      provide: MSAL_INSTANCE,
      useFactory: MSALInstanceFactory
    },
    {
      provide: MSAL_GUARD_CONFIG,
      useFactory: MSALGuardConfigFactory
    },
    {
      provide: MSAL_INTERCEPTOR_CONFIG,
      useFactory: MSALInterceptorConfigFactory
    },
    
    // MSAL Services
    MsalService,
    MsalGuard,
    MsalBroadcastService,
    
    // Application Services
    AuthService,
    AuthGuard
  ]
})
export class CoreModule {
  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {
    if (parentModule) {
      throw new Error('CoreModule is already loaded. Import it in the AppModule only.');
    }
  }
}

/**
 * MSAL Instance Factory
 */
export function MSALInstanceFactory(): IPublicClientApplication {
  console.log('Creating MSAL instance with config:', msalConfig);
  try {
    const instance = new PublicClientApplication(msalConfig);
    console.log('MSAL instance created successfully:', instance);
    return instance;
  } catch (error) {
    console.error('Error creating MSAL instance:', error);
    throw error;
  }
}

/**
 * MSAL Interceptor Configuration Factory
 */
export function MSALInterceptorConfigFactory(): MsalInterceptorConfiguration {
  const protectedResourceMap = new Map<string, Array<string>>();
  
  // Add MarriageBio API endpoints - update the URL pattern when you have your API
  // protectedResourceMap.set('https://your-api-domain.com/api/*', ['https://commonb2c5047.onmicrosoft.com/marriage-bio-api/access']);

  return {
    interactionType: InteractionType.Redirect,
    protectedResourceMap
  };
}

/**
 * MSAL Guard Configuration Factory
 */
export function MSALGuardConfigFactory(): MsalGuardConfiguration {
  return { 
    interactionType: InteractionType.Redirect,
    authRequest: loginRequest
  };
}
