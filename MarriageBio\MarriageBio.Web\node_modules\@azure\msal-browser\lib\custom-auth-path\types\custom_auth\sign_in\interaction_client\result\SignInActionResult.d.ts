import { AuthenticationResult } from "../../../../response/AuthenticationResult.js";
interface SignInActionResult {
    type: string;
    correlationId: string;
}
interface SignInContinuationTokenResult extends SignInActionResult {
    continuationToken: string;
}
export interface SignInCompletedResult extends SignInActionResult {
    type: typeof SIGN_IN_COMPLETED_RESULT_TYPE;
    authenticationResult: AuthenticationResult;
}
export interface SignInPasswordRequiredResult extends SignInContinuationTokenResult {
    type: typeof SIGN_IN_PASSWORD_REQUIRED_RESULT_TYPE;
}
export interface SignInCodeSendResult extends SignInContinuationTokenResult {
    type: typeof SIGN_IN_CODE_SEND_RESULT_TYPE;
    challengeChannel: string;
    challengeTargetLabel: string;
    codeLength: number;
    bindingMethod: string;
}
export declare const SIGN_IN_CODE_SEND_RESULT_TYPE = "SignInCodeSendResult";
export declare const SIGN_IN_PASSWORD_REQUIRED_RESULT_TYPE = "SignInPasswordRequiredResult";
export declare const SIGN_IN_COMPLETED_RESULT_TYPE = "SignInCompletedResult";
export declare function createSignInCompleteResult(input: Omit<SignInCompletedResult, "type">): SignInCompletedResult;
export declare function createSignInPasswordRequiredResult(input: Omit<SignInPasswordRequiredResult, "type">): SignInPasswordRequiredResult;
export declare function createSignInCodeSendResult(input: Omit<SignInCodeSendResult, "type">): SignInCodeSendResult;
export {};
//# sourceMappingURL=SignInActionResult.d.ts.map