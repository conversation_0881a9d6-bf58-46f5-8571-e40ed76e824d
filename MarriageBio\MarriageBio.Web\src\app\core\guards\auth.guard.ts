import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router, UrlTree } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { MsalGuard } from '@azure/msal-angular';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private msalGuard: MsalGuard,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    
    // Use MSAL Guard for authentication check
    return this.msalGuard.canActivate(route, state).pipe(
      map((result: boolean | UrlTree) => {
        if (typeof result === 'boolean' && !result) {
          // If not authenticated, redirect to login
          this.authService.loginRedirect();
          return false;
        }
        return result;
      }),
      catchError((error) => {
        console.error('Authentication guard error:', error);
        this.router.navigate(['/']);
        return of(false);
      })
    );
  }
}

/**
 * Alternative simple auth guard that uses our AuthService
 */
@Injectable({
  providedIn: 'root'
})
export class SimpleAuthGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    _route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    return this.authService.isAuthenticated$.pipe(
      map((isAuthenticated: boolean) => {
        if (!isAuthenticated) {
          // Store the attempted URL for redirecting after login
          sessionStorage.setItem('redirectUrl', state.url);
          this.authService.loginRedirect();
          return false;
        }
        return true;
      })
    );
  }
}
