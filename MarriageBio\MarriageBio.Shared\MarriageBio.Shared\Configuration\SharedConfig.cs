namespace MarriageBio.Shared.Configuration
{
    /// <summary>
    /// Optimized SharedConfig with base values only.
    /// Derived values (<PERSON><PERSON>, Authority, SignedOutCallbackPath) are computed from base values.
    /// </summary>
    public class SharedConfig
    {
        public string ApiBaseUrl { get; set; }
        public string MemberTypeRoleClaimName { get; set; }
        public string MemberTypeRoleClaimFreeValue { get; set; }
        public string MemberTypeRoleClaimPaidValue { get; set; }

        public AzureAdB2CConfig AzureAdB2C { get; set; }
        public AwsConfig Aws { get; set; }
        public AzureConfig Azure { get; set; }
        public string Sentinel { get; set; }
    }

    public class AzureAdB2CConfig
    {
        public ApiConfig Api { get; set; }
        public ApiWebConsumerConfig ApiWebConsumer { get; set; }
    }

    /// <summary>
    /// Base Azure B2C API configuration. All derived values are computed from these base properties.
    /// </summary>
    public class ApiConfig
    {
        /// <summary>Base Azure B2C instance URL</summary>
        public string Instance { get; set; }

        /// <summary>Base Azure B2C domain</summary>
        public string Domain { get; set; }

        /// <summary>API Client ID</summary>
        public string ClientId { get; set; }

        /// <summary>Sign up/sign in policy ID</summary>
        public string SignUpSignInPolicyId { get; set; }

        // Computed properties - derived from base values

        /// <summary>Computed: API scope derived from Domain</summary>
        public string Scope => $"https://{Domain}/marriage-bio-api/access";

        /// <summary>Computed: Signed out callback path derived from Policy</summary>
        public string SignedOutCallbackPath => $"/signout/{SignUpSignInPolicyId}";
    }

    /// <summary>
    /// Web consumer configuration with computed authority.
    /// </summary>
    public class ApiWebConsumerConfig
    {
        /// <summary>Web Client ID</summary>
        public string ClientId { get; set; }

        /// <summary>Always true - can be hardcoded</summary>
        public bool ValidateAuthority => true;

        /// <summary>Computed: Authority derived from API config</summary>
        public string GetAuthority(ApiConfig apiConfig) => $"{apiConfig.Instance}/{apiConfig.Domain}/{apiConfig.SignUpSignInPolicyId}";
    }

    /// <summary>
    /// AWS configuration settings
    /// </summary>
    public class AwsConfig
    {
        /// <summary>AWS Access Key ID</summary>
        public string AccessKeyId { get; set; }

        /// <summary>AWS Account ID</summary>
        public string AccountId { get; set; }

        /// <summary>AWS Region</summary>
        public string Region { get; set; }

        /// <summary>AWS Secret Access Key (typically stored in Key Vault)</summary>
        public string SecretAccessKey { get; set; }
    }

    /// <summary>
    /// Azure configuration settings
    /// </summary>
    public class AzureConfig
    {
        /// <summary>Application Insights Connection String (typically stored in Key Vault)</summary>
        public string ApplicationInsightsConnectionString { get; set; }

        /// <summary>Azure Resource Name Prefix</summary>
        public string ResourceNamePrefix { get; set; }
    }
}

