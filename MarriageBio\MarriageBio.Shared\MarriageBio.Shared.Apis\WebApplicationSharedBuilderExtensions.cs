using Azure.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.AzureAppConfiguration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Web;
using MarriageBio.Shared.Apis;
using MarriageBio.Shared.Configuration; 

namespace MarriageBio.Shared.Apis
{
    public static class WebApplicationSharedBuilderExtensions
    {
        public static WebApplicationBuilder ConfigureSharedWebApplicationBuilder(this WebApplicationBuilder builder)
        {
            builder.Services.AddControllers();
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen();
            builder.Services.AddAzureAppConfiguration();
            builder.AddConfiguration("Shared");
            SharedConfig sharedConfig = builder.BindConfiguration<SharedConfig>("Shared");
            builder.AddApplicationInsights(sharedConfig);
            builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
            builder.Services.AddProblemDetails();

            builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddMicrosoftIdentityWebApi(
                    options =>
                    {
                        options.TokenValidationParameters.NameClaimType = "name";
                    },
                    options =>
                    {
                        options.Instance = sharedConfig.AzureAdB2C.Instance;
                        options.Domain = sharedConfig.AzureAdB2C.Domain;
                        options.ClientId = sharedConfig.AzureAdB2C.ApiClientId;
                        options.SignedOutCallbackPath = sharedConfig.AzureAdB2C.SignedOutCallbackPath;
                        options.SignUpSignInPolicyId = sharedConfig.AzureAdB2C.SignUpSignInPolicyId;
                    }
                );

            builder.Services.AddAuthorization(options =>
            {
                options.AddPolicy("Free", policy =>
                {
                    policy.RequireClaim(
                        sharedConfig.AzureAdB2C.MemberTypeRoleClaimName,
                        sharedConfig.AzureAdB2C.MemberTypeRoleClaimFreeValue
                    );
                });

                options.AddPolicy("Paid", policy =>
                {
                    policy.RequireClaim(
                        sharedConfig.AzureAdB2C.MemberTypeRoleClaimName,
                        sharedConfig.AzureAdB2C.MemberTypeRoleClaimPaidValue
                    );
                });
            });

            return builder;
        }

        public static WebApplication ConfigureSharedWebApplication(this WebApplicationBuilder builder)
        {
            var app = builder.Build();
            app.UseExceptionHandler();
            app.UseAzureAppConfiguration();
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }
            app.UseHttpsRedirection();
            app.UseAuthentication();
            app.UseAuthorization();
            app.MapControllers();
            return app;
        }

        public static WebApplicationBuilder AddConfiguration(this WebApplicationBuilder builder, string sectionName)
        {
            string appConfigEndpoint = builder.Configuration.GetValue<string>("AZURE_APP_CONFIG_ENDPOINT")
                ?? throw new Exception("App Configuration endpoint is missing.");
            builder.Configuration.AddAzureAppConfiguration(options =>
            {
                var credential = new DefaultAzureCredential();
                options.Connect(new Uri(appConfigEndpoint), credential);
                options.ConfigureKeyVault(kv => kv.SetCredential(credential));
                options.Select($"MarriageBio:{sectionName}:*", LabelFilter.Null);
                options.ConfigureRefresh(refreshOptions =>
                    refreshOptions.Register("MarriageBio:Shared:Sentinel", refreshAll: true));
            });
            return builder;
        }

        public static T BindConfiguration<T>(this WebApplicationBuilder builder, string sectionName) where T : class
        {
            var config = builder.Configuration.GetSection($"MarriageBio:{sectionName}").Get<T>();
            builder.Services.Configure<T>(builder.Configuration.GetSection($"MarriageBio:{sectionName}"));
            return config;
        }

        private static WebApplicationBuilder AddApplicationInsights(this WebApplicationBuilder builder, SharedConfig sharedConfig)
        {
            string connectionString = sharedConfig?.ApplicationInsightsConnectionString
                ?? throw new Exception("Application Insights connection string is missing.");
            builder.Logging.AddApplicationInsights(
                configureTelemetryConfiguration: config => config.ConnectionString = connectionString,
                configureApplicationInsightsLoggerOptions: _ => { }
            );
            return builder;
        }
    }
}

