{"version": 3, "file": "PublicApiId.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/custom_auth/core/telemetry/PublicApiId.ts"], "names": [], "mappings": "AAWA,eAAO,MAAM,uBAAuB,SAAS,CAAC;AAC9C,eAAO,MAAM,2BAA2B,SAAS,CAAC;AAClD,eAAO,MAAM,mBAAmB,SAAS,CAAC;AAC1C,eAAO,MAAM,uBAAuB,SAAS,CAAC;AAC9C,eAAO,MAAM,mBAAmB,SAAS,CAAC;AAC1C,eAAO,MAAM,qBAAqB,SAAS,CAAC;AAC5C,eAAO,MAAM,4BAA4B,SAAS,CAAC;AAGnD,eAAO,MAAM,2BAA2B,SAAS,CAAC;AAClD,eAAO,MAAM,aAAa,SAAS,CAAC;AACpC,eAAO,MAAM,mBAAmB,SAAS,CAAC;AAC1C,eAAO,MAAM,uBAAuB,SAAS,CAAC;AAC9C,eAAO,MAAM,yBAAyB,SAAS,CAAC;AAChD,eAAO,MAAM,mBAAmB,SAAS,CAAC;AAG1C,eAAO,MAAM,oBAAoB,SAAS,CAAC;AAC3C,eAAO,MAAM,0BAA0B,SAAS,CAAC;AACjD,eAAO,MAAM,8BAA8B,SAAS,CAAC;AACrD,eAAO,MAAM,0BAA0B,SAAS,CAAC;AAGjD,eAAO,MAAM,mBAAmB,SAAS,CAAC;AAC1C,eAAO,MAAM,gBAAgB,SAAS,CAAC;AACvC,eAAO,MAAM,wBAAwB,SAAS,CAAC"}