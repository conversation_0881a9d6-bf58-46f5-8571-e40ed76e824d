import { AuthError } from "@azure/msal-common/browser";
/**
 * NestedAppAuthErrorMessage class containing string constants used by error codes and messages.
 */
export declare const NestedAppAuthErrorMessage: {
    unsupportedMethod: {
        code: string;
        desc: string;
    };
};
export declare class NestedAppAuthError extends AuthError {
    constructor(errorCode: string, errorMessage?: string);
    static createUnsupportedError(): NestedAppAuthError;
}
//# sourceMappingURL=NestedAppAuthError.d.ts.map