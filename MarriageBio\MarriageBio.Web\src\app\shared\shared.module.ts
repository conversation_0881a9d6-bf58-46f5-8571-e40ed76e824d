import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// Common components that might be shared across features
// import { LoadingSpinnerComponent } from './components/loading-spinner/loading-spinner.component';
// import { ConfirmDialogComponent } from './components/confirm-dialog/confirm-dialog.component';

// Common pipes
// import { TruncatePipe } from './pipes/truncate.pipe';

// Common directives
// import { HighlightDirective } from './directives/highlight.directive';

/**
 * Shared module that contains common components, pipes, and directives
 * that are used across multiple feature modules.
 */
@NgModule({
  declarations: [
    // Add shared components, pipes, and directives here
    // LoadingSpinnerComponent,
    // ConfirmDialogComponent,
    // TruncatePipe,
    // HighlightDirective
  ],
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FormsModule
  ],
  exports: [
    // Re-export common modules
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FormsModule,
    
    // Export shared components, pipes, and directives
    // LoadingSpinnerComponent,
    // ConfirmDialogComponent,
    // TruncatePipe,
    // HighlightDirective
  ]
})
export class SharedModule { }
