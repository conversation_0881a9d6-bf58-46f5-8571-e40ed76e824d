{"version": 3, "sources": ["../../../../../../node_modules/@azure/msal-browser/dist/telemetry/BrowserPerformanceMeasurement.mjs"], "sourcesContent": ["/*! @azure/msal-browser v4.19.0 2025-08-05 */\n'use strict';\n\n/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\nclass BrowserPerformanceMeasurement {\n  constructor(name, correlationId) {\n    this.correlationId = correlationId;\n    this.measureName = BrowserPerformanceMeasurement.makeMeasureName(name, correlationId);\n    this.startMark = BrowserPerformanceMeasurement.makeStartMark(name, correlationId);\n    this.endMark = BrowserPerformanceMeasurement.makeEndMark(name, correlationId);\n  }\n  static makeMeasureName(name, correlationId) {\n    return `msal.measure.${name}.${correlationId}`;\n  }\n  static makeStartMark(name, correlationId) {\n    return `msal.start.${name}.${correlationId}`;\n  }\n  static makeEndMark(name, correlationId) {\n    return `msal.end.${name}.${correlationId}`;\n  }\n  static supportsBrowserPerformance() {\n    return typeof window !== \"undefined\" && typeof window.performance !== \"undefined\" && typeof window.performance.mark === \"function\" && typeof window.performance.measure === \"function\" && typeof window.performance.clearMarks === \"function\" && typeof window.performance.clearMeasures === \"function\" && typeof window.performance.getEntriesByName === \"function\";\n  }\n  /**\r\n   * Flush browser marks and measurements.\r\n   * @param {string} correlationId\r\n   * @param {SubMeasurement} measurements\r\n   */\n  static flushMeasurements(correlationId, measurements) {\n    if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\n      try {\n        measurements.forEach(measurement => {\n          const measureName = BrowserPerformanceMeasurement.makeMeasureName(measurement.name, correlationId);\n          const entriesForMeasurement = window.performance.getEntriesByName(measureName, \"measure\");\n          if (entriesForMeasurement.length > 0) {\n            window.performance.clearMeasures(measureName);\n            window.performance.clearMarks(BrowserPerformanceMeasurement.makeStartMark(measureName, correlationId));\n            window.performance.clearMarks(BrowserPerformanceMeasurement.makeEndMark(measureName, correlationId));\n          }\n        });\n      } catch (e) {\n        // Silently catch and return null\n      }\n    }\n  }\n  startMeasurement() {\n    if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\n      try {\n        window.performance.mark(this.startMark);\n      } catch (e) {\n        // Silently catch\n      }\n    }\n  }\n  endMeasurement() {\n    if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\n      try {\n        window.performance.mark(this.endMark);\n        window.performance.measure(this.measureName, this.startMark, this.endMark);\n      } catch (e) {\n        // Silently catch\n      }\n    }\n  }\n  flushMeasurement() {\n    if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\n      try {\n        const entriesForMeasurement = window.performance.getEntriesByName(this.measureName, \"measure\");\n        if (entriesForMeasurement.length > 0) {\n          const durationMs = entriesForMeasurement[0].duration;\n          window.performance.clearMeasures(this.measureName);\n          window.performance.clearMarks(this.startMark);\n          window.performance.clearMarks(this.endMark);\n          return durationMs;\n        }\n      } catch (e) {\n        // Silently catch and return null\n      }\n    }\n    return null;\n  }\n}\nexport { BrowserPerformanceMeasurement };\n"], "mappings": ";AAOA,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,YAAY,MAAM,eAAe;AAC/B,SAAK,gBAAgB;AACrB,SAAK,cAAc,+BAA8B,gBAAgB,MAAM,aAAa;AACpF,SAAK,YAAY,+BAA8B,cAAc,MAAM,aAAa;AAChF,SAAK,UAAU,+BAA8B,YAAY,MAAM,aAAa;AAAA,EAC9E;AAAA,EACA,OAAO,gBAAgB,MAAM,eAAe;AAC1C,WAAO,gBAAgB,IAAI,IAAI,aAAa;AAAA,EAC9C;AAAA,EACA,OAAO,cAAc,MAAM,eAAe;AACxC,WAAO,cAAc,IAAI,IAAI,aAAa;AAAA,EAC5C;AAAA,EACA,OAAO,YAAY,MAAM,eAAe;AACtC,WAAO,YAAY,IAAI,IAAI,aAAa;AAAA,EAC1C;AAAA,EACA,OAAO,6BAA6B;AAClC,WAAO,OAAO,WAAW,eAAe,OAAO,OAAO,gBAAgB,eAAe,OAAO,OAAO,YAAY,SAAS,cAAc,OAAO,OAAO,YAAY,YAAY,cAAc,OAAO,OAAO,YAAY,eAAe,cAAc,OAAO,OAAO,YAAY,kBAAkB,cAAc,OAAO,OAAO,YAAY,qBAAqB;AAAA,EAC5V;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,kBAAkB,eAAe,cAAc;AACpD,QAAI,+BAA8B,2BAA2B,GAAG;AAC9D,UAAI;AACF,qBAAa,QAAQ,iBAAe;AAClC,gBAAM,cAAc,+BAA8B,gBAAgB,YAAY,MAAM,aAAa;AACjG,gBAAM,wBAAwB,OAAO,YAAY,iBAAiB,aAAa,SAAS;AACxF,cAAI,sBAAsB,SAAS,GAAG;AACpC,mBAAO,YAAY,cAAc,WAAW;AAC5C,mBAAO,YAAY,WAAW,+BAA8B,cAAc,aAAa,aAAa,CAAC;AACrG,mBAAO,YAAY,WAAW,+BAA8B,YAAY,aAAa,aAAa,CAAC;AAAA,UACrG;AAAA,QACF,CAAC;AAAA,MACH,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,QAAI,+BAA8B,2BAA2B,GAAG;AAC9D,UAAI;AACF,eAAO,YAAY,KAAK,KAAK,SAAS;AAAA,MACxC,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,QAAI,+BAA8B,2BAA2B,GAAG;AAC9D,UAAI;AACF,eAAO,YAAY,KAAK,KAAK,OAAO;AACpC,eAAO,YAAY,QAAQ,KAAK,aAAa,KAAK,WAAW,KAAK,OAAO;AAAA,MAC3E,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,QAAI,+BAA8B,2BAA2B,GAAG;AAC9D,UAAI;AACF,cAAM,wBAAwB,OAAO,YAAY,iBAAiB,KAAK,aAAa,SAAS;AAC7F,YAAI,sBAAsB,SAAS,GAAG;AACpC,gBAAM,aAAa,sBAAsB,CAAC,EAAE;AAC5C,iBAAO,YAAY,cAAc,KAAK,WAAW;AACjD,iBAAO,YAAY,WAAW,KAAK,SAAS;AAC5C,iBAAO,YAAY,WAAW,KAAK,OAAO;AAC1C,iBAAO;AAAA,QACT;AAAA,MACF,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;", "names": []}