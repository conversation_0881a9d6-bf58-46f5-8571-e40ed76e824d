<header class="header">
  <div class="logo">{{ title }}</div>
  <div class="auth-section">
    @if (isAuthenticated$ | async; as isAuthenticated) {
      @if (isAuthenticated) {
        @if (userProfile$ | async; as user) {
          <span class="user-greeting">
            Hello, {{ user.name || user.username || 'User' }}
          </span>
        }
        <button class="btn btn-secondary" (click)="logout()">Logout</button>
      } @else {
        <button class="btn btn-primary" (click)="login()">Login</button>
      }
    } @else {
      <!-- Fallback: Show login button if authentication state is undefined -->
      <button class="btn btn-primary" (click)="login()">Login</button>
    }
  </div>
</header>
