import { Configuration, BrowserCacheLocation } from '@azure/msal-browser';

/**
 * MarriageBio Azure B2C Configuration
 *
 * This file contains all configuration variables for the MarriageBio application's
 * Azure B2C authentication setup. The configuration is based on the following
 * application settings structure (only authentication-related settings):
 *
 * MarriageBio:Shared:AzureAdB2C:Api:ClientId
 * MarriageBio:Shared:AzureAdB2C:Api:Domain
 * MarriageBio:Shared:AzureAdB2C:Api:Instance
 * MarriageBio:Shared:AzureAdB2C:Api:SignUpSignInPolicyId
 * MarriageBio:Shared:AzureAdB2C:Web:ClientId
 *
 * All other values (scope, authority, callback paths) are derived from these base values.
 */

/**
 * Interface for MarriageBio configuration structure
 */
export interface MarriageBioConfig {
  azureB2C: {
    api: {
      clientId: string;
      domain: string;
      instance: string;
      scope: string;
      signUpSignInPolicyId: string;
      signedOutCallbackPath: string;
    };
    web: {
      authority: string;
      clientId: string;
      validateAuthority: boolean;
    };
  };
}

/**
 * MarriageBio Azure B2C Configuration Variables
 * Base configuration values - all other values are derived from these
 */

// Base Azure B2C Configuration
export const AZURE_B2C_DOMAIN = "commonb2c5047.onmicrosoft.com";
export const AZURE_B2C_INSTANCE = "https://commonb2c5047.b2clogin.com";
export const AZURE_B2C_POLICY_SIGN_UP_SIGN_IN = "B2C_1_signupsignin";

// Client IDs
export const AZURE_B2C_API_CLIENT_ID = "122316e1-be79-42ea-813d-8791c8479353";
export const AZURE_B2C_WEB_CLIENT_ID = "dc7ce323-2e2e-4155-8de7-116b12182747";

// Derived values (computed from base values)
export const AZURE_B2C_API_SCOPE = `https://${AZURE_B2C_DOMAIN}/marriage-bio-api/access`;
export const AZURE_B2C_WEB_AUTHORITY = `${AZURE_B2C_INSTANCE}/${AZURE_B2C_DOMAIN}/${AZURE_B2C_POLICY_SIGN_UP_SIGN_IN}`;
export const AZURE_B2C_SIGNED_OUT_CALLBACK_PATH = `/signout/${AZURE_B2C_POLICY_SIGN_UP_SIGN_IN}`;
export const AZURE_B2C_KNOWN_AUTHORITY = AZURE_B2C_INSTANCE.replace('https://', '');

/**
 * Complete MarriageBio configuration object
 */
export const marriageBioConfig: MarriageBioConfig = {
  azureB2C: {
    api: {
      clientId: AZURE_B2C_API_CLIENT_ID,
      domain: AZURE_B2C_DOMAIN,
      instance: AZURE_B2C_INSTANCE,
      scope: AZURE_B2C_API_SCOPE,
      signUpSignInPolicyId: AZURE_B2C_POLICY_SIGN_UP_SIGN_IN,
      signedOutCallbackPath: AZURE_B2C_SIGNED_OUT_CALLBACK_PATH
    },
    web: {
      authority: AZURE_B2C_WEB_AUTHORITY,
      clientId: AZURE_B2C_WEB_CLIENT_ID,
      validateAuthority: true
    }
  }
};

/**
 * Configuration object to be passed to MSAL instance on creation.
 * For a full list of MSAL.js configuration parameters, visit:
 * https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-browser/docs/configuration.md
 */
export const msalConfig: Configuration = {
  auth: {
    clientId: AZURE_B2C_WEB_CLIENT_ID,
    authority: AZURE_B2C_WEB_AUTHORITY,
    knownAuthorities: [AZURE_B2C_KNOWN_AUTHORITY],
    redirectUri: window.location.origin,
    postLogoutRedirectUri: window.location.origin,
  },
  cache: {
    cacheLocation: BrowserCacheLocation.LocalStorage,
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) {
          return;
        }
        switch (level) {
          case 0: // LogLevel.Error
            console.error(message);
            return;
          case 1: // LogLevel.Warning
            console.warn(message);
            return;
          case 2: // LogLevel.Info
            console.info(message);
            return;
          case 3: // LogLevel.Verbose
            console.debug(message);
            return;
        }
      }
    }
  }
};

/**
 * Scopes you add here will be prompted for user consent during sign-in.
 * By default, MSAL.js will add OIDC scopes (openid, profile, email) to any login request.
 * For more information about OIDC scopes, visit:
 * https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-permissions-and-consent#openid-connect-scopes
 */
export const loginRequest = {
  scopes: ['openid', 'profile'],
};

/**
 * An optional silentRequest object can be used to achieve silent SSO
 * between applications by providing a "login_hint" property.
 */
export const silentRequest = {
  scopes: ['openid', 'profile'],
  loginHint: '<EMAIL>' // For B2C, this should be the email/username
};

/**
 * API scope configuration for accessing MarriageBio API
 */
export const apiRequest = {
  scopes: [AZURE_B2C_API_SCOPE],
};

/**
 * Azure B2C configuration object using the defined variables
 * This is a simplified version of marriageBioConfig for direct B2C access
 */
export const azureB2CConfig = {
  api: {
    clientId: AZURE_B2C_API_CLIENT_ID,
    domain: AZURE_B2C_DOMAIN,
    instance: AZURE_B2C_INSTANCE,
    scope: AZURE_B2C_API_SCOPE,
    signUpSignInPolicyId: AZURE_B2C_POLICY_SIGN_UP_SIGN_IN,
    signedOutCallbackPath: AZURE_B2C_SIGNED_OUT_CALLBACK_PATH
  },
  web: {
    authority: AZURE_B2C_WEB_AUTHORITY,
    clientId: AZURE_B2C_WEB_CLIENT_ID,
    validateAuthority: true
  }
};

/**
 * B2C policies configuration using the defined variables
 */
export const b2cPolicies = {
  names: {
    signUpSignIn: AZURE_B2C_POLICY_SIGN_UP_SIGN_IN,
    editProfile: 'B2C_1_EDIT_PROFILE', // Optional - update if you have this policy
    resetPassword: 'B2C_1_RESET_PASSWORD', // Optional - update if you have this policy
  },
  authorities: {
    signUpSignIn: {
      authority: AZURE_B2C_WEB_AUTHORITY,
    },
    editProfile: {
      authority: `${AZURE_B2C_INSTANCE}/${AZURE_B2C_DOMAIN}/B2C_1_EDIT_PROFILE`,
    },
    resetPassword: {
      authority: `${AZURE_B2C_INSTANCE}/${AZURE_B2C_DOMAIN}/B2C_1_RESET_PASSWORD`,
    },
  },
  authorityDomain: AZURE_B2C_KNOWN_AUTHORITY
};

/**
 * Utility functions for configuration management
 */
export const configUtils = {
  /**
   * Get the full authority URL for a specific policy
   */
  getAuthorityForPolicy: (policyName: string): string => {
    return `${AZURE_B2C_INSTANCE}/${AZURE_B2C_DOMAIN}/${policyName}`;
  },

  /**
   * Get the known authorities array for MSAL configuration
   */
  getKnownAuthorities: (): string[] => {
    return [AZURE_B2C_KNOWN_AUTHORITY];
  },

  /**
   * Validate that all required configuration values are present
   */
  validateConfig: (): boolean => {
    const requiredValues = [
      AZURE_B2C_WEB_CLIENT_ID,
      AZURE_B2C_WEB_AUTHORITY,
      AZURE_B2C_API_CLIENT_ID,
      AZURE_B2C_DOMAIN,
      AZURE_B2C_INSTANCE,
      AZURE_B2C_API_SCOPE,
      AZURE_B2C_POLICY_SIGN_UP_SIGN_IN
    ];

    return requiredValues.every(value => value && value.trim() !== '');
  },

  /**
   * Get environment-specific configuration (for future use)
   */
  getEnvironmentConfig: (): Partial<MarriageBioConfig> => {
    // This can be extended to support different environments
    // For now, return the default configuration
    return marriageBioConfig;
  }
};
