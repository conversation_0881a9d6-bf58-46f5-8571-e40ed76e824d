# Azure B2C Authentication Setup Guide

This guide will help you configure Azure B2C authentication for your Angular application.

## Prerequisites

1. An Azure subscription
2. Access to Azure Active Directory B2C

## Step 1: Create Azure B2C Tenant

1. Go to the [Azure Portal](https://portal.azure.com)
2. Search for "Azure AD B2C" and select it
3. Click "Create a new B2C Tenant"
4. <PERSON><PERSON> "Create a new Azure AD B2C Tenant"
5. Fill in the required information:
   - Organization name: `MarriageBio`
   - Initial domain name: `marriagebio` (this will create marriagebio.onmicrosoft.com)
   - Country/Region: Select your country
6. Click "Review + create" and then "Create"

## Step 2: Register Your Application

1. In your B2C tenant, go to "App registrations"
2. Click "New registration"
3. Fill in the details:
   - Name: `MarriageBio Web App`
   - Supported account types: `Accounts in any identity provider or organizational directory`
   - Redirect URI: 
     - Type: `Single-page application (SPA)`
     - URL: `http://localhost:4200/`
4. Click "Register"
5. Note down the **Application (client) ID** - you'll need this later

## Step 3: Configure Authentication

1. In your app registration, go to "Authentication"
2. Under "Single-page application", add these redirect URIs:
   - `http://localhost:4200/`
   - `https://yourdomain.com/` (for production)
3. Under "Logout URLs", add:
   - `http://localhost:4200/`
4. Enable "Access tokens" and "ID tokens" under "Implicit grant and hybrid flows"
5. Click "Save"

## Step 4: Create User Flows

### Sign-up and Sign-in Flow
1. Go to "User flows" in your B2C tenant
2. Click "New user flow"
3. Select "Sign up and sign in" and choose "Recommended"
4. Give it a name: `B2C_1_SIGN_UP_SIGN_IN`
5. Under "Identity providers", select "Email signup"
6. Under "User attributes and claims":
   - Collect: Email Address, Display Name, Given Name, Surname
   - Return: Email Addresses, Display Name, Given Name, Surname, User's Object ID
7. Click "Create"

### Profile Editing Flow (Optional)
1. Click "New user flow"
2. Select "Profile editing" and choose "Recommended"
3. Give it a name: `B2C_1_EDIT_PROFILE`
4. Configure similar to the sign-up flow
5. Click "Create"

### Password Reset Flow (Optional)
1. Click "New user flow"
2. Select "Password reset" and choose "Recommended"
3. Give it a name: `B2C_1_RESET_PASSWORD`
4. Configure as needed
5. Click "Create"

## Step 5: Update Configuration

Update the `src/app/auth-config.ts` file with your Azure B2C details:

```typescript
export const msalConfig: Configuration = {
  auth: {
    clientId: 'YOUR_CLIENT_ID', // Replace with your Application (client) ID
    authority: 'https://YOUR_TENANT_NAME.b2clogin.com/YOUR_TENANT_NAME.onmicrosoft.com/B2C_1_SIGN_UP_SIGN_IN',
    knownAuthorities: ['YOUR_TENANT_NAME.b2clogin.com'],
    redirectUri: '/',
    postLogoutRedirectUri: '/',
  },
  // ... rest of the configuration
};

export const b2cPolicies = {
  names: {
    signUpSignIn: 'B2C_1_SIGN_UP_SIGN_IN',
    editProfile: 'B2C_1_EDIT_PROFILE',
    resetPassword: 'B2C_1_RESET_PASSWORD',
  },
  authorities: {
    signUpSignIn: {
      authority: 'https://YOUR_TENANT_NAME.b2clogin.com/YOUR_TENANT_NAME.onmicrosoft.com/B2C_1_SIGN_UP_SIGN_IN',
    },
    editProfile: {
      authority: 'https://YOUR_TENANT_NAME.b2clogin.com/YOUR_TENANT_NAME.onmicrosoft.com/B2C_1_EDIT_PROFILE',
    },
    resetPassword: {
      authority: 'https://YOUR_TENANT_NAME.b2clogin.com/YOUR_TENANT_NAME.onmicrosoft.com/B2C_1_RESET_PASSWORD',
    },
  },
  authorityDomain: 'YOUR_TENANT_NAME.b2clogin.com'
};
```

Replace the following placeholders:
- `YOUR_CLIENT_ID`: The Application (client) ID from Step 2
- `YOUR_TENANT_NAME`: Your B2C tenant name (e.g., `marriagebio`)

## Step 6: Test the Application

1. Start your Angular application: `ng serve`
2. Navigate to `http://localhost:4200`
3. Click "Sign In" to test the authentication flow
4. Try accessing the protected `/profile` route

## Features Implemented

✅ **Authentication Service** - Handles login, logout, and token management
✅ **Route Guards** - Protects routes that require authentication
✅ **User Profile Display** - Shows authenticated user information
✅ **Protected Routes** - Example profile page that requires authentication
✅ **Responsive UI** - Clean, modern interface with authentication status

## Available Routes

- `/` - Home page (public)
- `/profile` - User profile page (protected, requires authentication)

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure your redirect URIs are correctly configured in Azure B2C
2. **Authority Not Found**: Verify your tenant name and policy names are correct
3. **Token Issues**: Check that your client ID matches the one in Azure B2C

### Debug Mode

The application includes console logging for authentication events. Check the browser console for detailed error messages.

## Production Deployment

For production deployment:

1. Update redirect URIs in Azure B2C to include your production domain
2. Update the `redirectUri` and `postLogoutRedirectUri` in `auth-config.ts`
3. Consider using environment-specific configuration files
4. Enable HTTPS for your production application

## Security Considerations

- Always use HTTPS in production
- Regularly rotate client secrets (if using confidential client)
- Monitor authentication logs in Azure B2C
- Implement proper error handling for authentication failures
- Consider implementing token refresh logic for long-running applications

## Next Steps

- Customize the B2C user interface to match your brand
- Add additional user attributes as needed
- Implement API protection using the same B2C tenant
- Set up monitoring and analytics for authentication events
